<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <title>费用业务运费拆账</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <link rel="stylesheet" href="https://unpkg.com/layui@2.8.18/dist/css/layui.css">
  <style>
    .container {
      padding: 20px;
      max-width: 1200px;
      margin: 0 auto;
    }

    .section {
      margin-bottom: 30px;
      border: 1px solid #e6e6e6;
      border-radius: 5px;
      padding: 20px;
    }

    .section-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 15px;
      color: #333;
    }

    .info-row {
      display: flex;
      margin-bottom: 10px;
    }

    .info-item {
      flex: 1;
      margin-right: 20px;
    }

    .info-item:last-child {
      margin-right: 0;
    }

    .info-label {
      display: inline-block;
      width: 100px;
      color: #666;
    }

    .info-value {
      color: #333;
    }

    .red-text {
      color: #ff0000;
    }

    .action-buttons {
      text-align: center;
      margin-top: 20px;
    }

    .action-buttons .layui-btn {
      margin: 0 10px;
    }

    .split-table {
      margin-top: 15px;
    }

    .add-row {
      color: #1E9FFF;
      cursor: pointer;
      margin-top: 10px;
    }

    .add-row:hover {
      text-decoration: underline;
    }
  </style>
</head>

<body>
  <div class="container">
    <!-- 基本信息 -->
    <div class="section">
      <div class="section-title">基本信息</div>
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">NC客商名称：</span>
          <span class="info-value">中国邮政集团有限公司福州市分公司</span>
        </div>
        <div class="info-item">
          <span class="info-label">NC编号：</span>
          <span class="info-value">N001616</span>
        </div>
      </div>
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">业务月份：</span>
          <span class="info-value">202503</span>
        </div>
        <div class="info-item">
          <span class="info-label">产品类型：</span>
          <span class="info-value">普货件</span>
        </div>
      </div>
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">对应业务大类：</span>
          <span class="info-value">手续费补贴</span>
        </div>
        <div class="info-item">
          <span class="info-label">业务类型：</span>
          <span class="info-value">手续费补贴</span>
        </div>
      </div>
    </div>

    <!-- 金额信息 -->
    <div class="section">
      <div class="section-title">金额信息</div>
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">业务金额：</span>
          <span class="info-value">10000.00</span>
        </div>
        <div class="info-item">
          <span class="info-label">调整金额：</span>
          <span class="info-value">-1000.00</span>
        </div>
      </div>
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">扣印花金额：</span>
          <span class="info-value">1000.00</span>
        </div>
        <div class="info-item">
          <span class="info-label">已开票金额：</span>
          <span class="info-value">1000.00</span>
        </div>
      </div>
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">已回款金额：</span>
          <span class="info-value">1000.00</span>
        </div>
        <div class="info-item">
          <span class="info-label">可拆账金额：</span>
          <span class="info-value red-text">7000.00</span>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <button type="button" class="layui-btn layui-btn-normal" id="splitApply">拆账申请</button>
      <button type="button" class="layui-btn layui-btn-warm" id="splitAudit">拆账审核</button>
      <button type="button" class="layui-btn" id="reSplit">重新拆账</button>
    </div>
  </div>

  <script src="https://unpkg.com/layui@2.8.18/dist/layui.js"></script>
  <script>
    layui.use(['layer', 'form', 'table'], function () {
      var layer = layui.layer;
      var form = layui.form;
      var table = layui.table;

      // 通用弹窗函数
      function openSplitDialog (type) {
        var title = '';
        var content = '';
        var readonly = false;
        var showAuditFields = false;

        switch (type) {
          case 'apply':
            title = '拆账申请';
            break;
          case 'audit':
            title = '拆账审核';
            readonly = true;
            showAuditFields = true;
            break;
          case 'resplit':
            title = '重新拆账';
            break;
        }

        content = generateDialogContent(type, readonly, showAuditFields);

        layer.open({
          type: 1,
          title: title,
          area: ['800px', '600px'],
          content: content,
          btn: readonly ? ['取消', '确认'] : ['取消', '确认'],
          yes: function (index, layero) {
            layer.close(index);
          },
          btn2: function (index, layero) {
            if (type === 'audit') {
              // 审核逻辑
              layer.msg('审核完成');
            } else {
              // 申请或重新拆账逻辑
              layer.msg('操作完成');
            }
            layer.close(index);
          }
        });
      }

      // 生成弹窗内容
      function generateDialogContent (type, readonly, showAuditFields) {
        var readonlyAttr = readonly ? 'readonly' : '';
        var disabledAttr = readonly ? 'disabled' : '';

        var html = `
                    <div style="padding: 20px;">
                        <div style="margin-bottom: 20px;">
                            <div style="margin-bottom: 15px;">
                                <div style="display: flex; margin-bottom: 10px;">
                                    <div style="flex: 1; margin-right: 20px;">
                                        <span style="display: inline-block; width: 100px;">NC客商名称：</span>
                                        <span>中国邮政集团有限公司福州市分公司</span>
                                    </div>
                                    <div style="flex: 1;">
                                        <span style="display: inline-block; width: 80px;">NC编号：</span>
                                        <span>N001616</span>
                                    </div>
                                </div>
                                <div style="display: flex; margin-bottom: 10px;">
                                    <div style="flex: 1; margin-right: 20px;">
                                        <span style="display: inline-block; width: 100px;">业务月份：</span>
                                        <span>202503</span>
                                    </div>
                                    <div style="flex: 1;">
                                        <span style="display: inline-block; width: 80px;">产品类型：</span>
                                        <span>普货件</span>
                                    </div>
                                </div>
                                <div style="display: flex; margin-bottom: 10px;">
                                    <div style="flex: 1; margin-right: 20px;">
                                        <span style="display: inline-block; width: 100px;">对应业务大类：</span>
                                        <span>手续费补贴</span>
                                    </div>
                                    <div style="flex: 1;">
                                        <span style="display: inline-block; width: 80px;">业务类型：</span>
                                        <span>手续费补贴</span>
                                    </div>
                                </div>
                            </div>

                            <div style="margin-bottom: 15px;">
                                <div style="display: flex; margin-bottom: 10px;">
                                    <div style="flex: 1; margin-right: 20px;">
                                        <span style="display: inline-block; width: 100px;">业务金额：</span>
                                        <span>10000.00</span>
                                    </div>
                                    <div style="flex: 1;">
                                        <span style="display: inline-block; width: 80px;">调整金额：</span>
                                        <span>-1000.00</span>
                                    </div>
                                </div>
                                <div style="display: flex; margin-bottom: 10px;">
                                    <div style="flex: 1; margin-right: 20px;">
                                        <span style="display: inline-block; width: 100px;">扣印花金额：</span>
                                        <span>1000.00</span>
                                    </div>
                                    <div style="flex: 1;">
                                        <span style="display: inline-block; width: 80px;">已开票金额：</span>
                                        <span>1000.00</span>
                                    </div>
                                </div>
                                <div style="display: flex; margin-bottom: 10px;">
                                    <div style="flex: 1; margin-right: 20px;">
                                        <span style="display: inline-block; width: 100px;">已回款金额：</span>
                                        <span>1000.00</span>
                                    </div>
                                    <div style="flex: 1;">
                                        <span style="display: inline-block; width: 80px;">可拆账金额：</span>
                                        <span style="color: red;">7000.00</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div style="margin-bottom: 20px;">
                            <div style="margin-bottom: 10px; font-weight: bold;">拆账明细</div>
                            <div style="display: flex; margin-bottom: 10px;">
                                <div style="flex: 1; margin-right: 20px;">
                                    <span style="display: inline-block; width: 100px;">拆账金额：</span>
                                    <span>7000.00；</span>
                                </div>
                                <div style="flex: 1;">
                                    <span style="display: inline-block; width: 80px;">拆账金额：</span>
                                    <span>3000.00</span>
                                </div>
                            </div>

                            <table class="layui-table" style="margin-top: 15px;">
                                <thead>
                                    <tr>
                                        <th style="width: 80px;">序号</th>
                                        <th>NC客商名称</th>
                                        <th style="width: 120px;">NC编号</th>
                                        <th style="width: 120px;">拆账金额</th>
                                        ${type === 'resplit' ? '<th style="width: 80px;">操作</th>' : ''}
                                    </tr>
                                </thead>
                                <tbody id="splitTableBody">
                                    <tr>
                                        <td>1</td>
                                        <td>
                                            ${readonly ? '中国邮政集团有限公司三明分公司' :
            '<select name="ncName1" lay-filter="ncName"><option value="">请选择</option><option value="中国邮政集团有限公司三明分公司" selected>中国邮政集团有限公司三明分公司</option></select>'}
                                        </td>
                                        <td>${readonly ? 'N1224' : '<input type="text" name="ncCode1" value="N1224" class="layui-input" ' + readonlyAttr + '>'}</td>
                                        <td>${readonly ? '2000' : '<input type="text" name="amount1" value="2000" class="layui-input" ' + readonlyAttr + '>'}</td>
                                        ${type === 'resplit' ? '<td><a href="javascript:;" class="layui-btn layui-btn-xs layui-btn-danger" onclick="deleteRow(this)">删除</a></td>' : ''}
                                    </tr>
                                    <tr>
                                        <td>2</td>
                                        <td>
                                            ${readonly ? '中国邮政集团有限公司三明分公司' :
            '<select name="ncName2" lay-filter="ncName"><option value="">请选择</option><option value="中国邮政集团有限公司三明分公司" selected>中国邮政集团有限公司三明分公司</option></select>'}
                                        </td>
                                        <td>${readonly ? 'N1225' : '<input type="text" name="ncCode2" value="N1225" class="layui-input" ' + readonlyAttr + '>'}</td>
                                        <td>${readonly ? '5000' : '<input type="text" name="amount2" value="5000" class="layui-input" ' + readonlyAttr + '>'}</td>
                                        ${type === 'resplit' ? '<td><a href="javascript:;" class="layui-btn layui-btn-xs layui-btn-danger" onclick="deleteRow(this)">删除</a></td>' : ''}
                                    </tr>
                                </tbody>
                            </table>

                            ${!readonly ? '<div style="margin-top: 10px;"><a href="javascript:;" style="color: #1E9FFF;" onclick="addRow()">+新增</a></div>' : ''}
                        </div>

                        ${showAuditFields ? `
                            <div style="margin-bottom: 20px;">
                                <div style="margin-bottom: 10px;">
                                    <span style="display: inline-block; width: 100px;">审核结果：</span>
                                    <input type="radio" name="auditResult" value="pass" title="审核通过" checked>
                                    <input type="radio" name="auditResult" value="reject" title="审核拒绝">
                                </div>
                                <div>
                                    <span style="display: inline-block; width: 100px; vertical-align: top;">审核原因：</span>
                                    <textarea name="auditReason" placeholder="请输入审核原因" class="layui-textarea" style="width: 300px; height: 80px;"></textarea>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                `;

        return html;
      }

      // 绑定按钮事件
      document.getElementById('splitApply').addEventListener('click', function () {
        openSplitDialog('apply');
      });

      document.getElementById('splitAudit').addEventListener('click', function () {
        openSplitDialog('audit');
      });

      document.getElementById('reSplit').addEventListener('click', function () {
        openSplitDialog('resplit');
      });

      // 全局函数：添加行
      window.addRow = function () {
        var tbody = document.getElementById('splitTableBody');
        var rowCount = tbody.children.length + 1;
        var newRow = `
                    <tr>
                        <td>${rowCount}</td>
                        <td>
                            <select name="ncName${rowCount}" lay-filter="ncName">
                                <option value="">请选择</option>
                                <option value="中国邮政集团有限公司三明分公司">中国邮政集团有限公司三明分公司</option>
                            </select>
                        </td>
                        <td><input type="text" name="ncCode${rowCount}" class="layui-input"></td>
                        <td><input type="text" name="amount${rowCount}" class="layui-input"></td>
                        <td><a href="javascript:;" class="layui-btn layui-btn-xs layui-btn-danger" onclick="deleteRow(this)">删除</a></td>
                    </tr>
                `;
        tbody.insertAdjacentHTML('beforeend', newRow);
        form.render('select');
      };

      // 全局函数：删除行
      window.deleteRow = function (btn) {
        var row = btn.closest('tr');
        row.remove();
        // 重新编号
        var tbody = document.getElementById('splitTableBody');
        var rows = tbody.children;
        for (var i = 0; i < rows.length; i++) {
          rows[i].children[0].textContent = i + 1;
        }
      };
    });
  </script>
</body>

</html>