<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <title>费用业务运费拆账</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <link rel="stylesheet" href="https://unpkg.com/layui@2.8.18/dist/css/layui.css">
  <style>
    .container {
      padding: 20px;
      max-width: 1200px;
      margin: 0 auto;
    }

    .section {
      margin-bottom: 30px;
      border: 1px solid #e6e6e6;
      border-radius: 5px;
      padding: 20px;
    }

    .section-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 15px;
      color: #333;
    }

    .info-row {
      display: flex;
      margin-bottom: 10px;
    }

    .info-item {
      flex: 1;
      margin-right: 20px;
    }

    .info-item:last-child {
      margin-right: 0;
    }

    .info-label {
      display: inline-block;
      width: 100px;
      color: #666;
    }

    .info-value {
      color: #333;
    }

    .red-text {
      color: #ff0000;
    }

    .action-buttons {
      text-align: center;
      margin-top: 20px;
    }

    .action-buttons .layui-btn {
      margin: 0 10px;
    }

    .split-table {
      margin-top: 15px;
    }

    .add-row {
      color: #1E9FFF;
      cursor: pointer;
      margin-top: 10px;
    }

    .add-row:hover {
      text-decoration: underline;
    }

    .dialog-section {
      margin-bottom: 20px;
    }

    .readonly-text {
      display: inline-block;
    }

    .editable-input,
    .editable-select {
      width: 100%;
    }

    /* 弹窗内容样式 */
    #splitDialog {
      padding: 20px !important;
    }

    /* 确保弹窗内的表格有适当间距 */
    #splitDialog .layui-table {
      margin: 15px 0;
    }

    /* 弹窗内信息行的间距 */
    #splitDialog .info-row {
      margin-bottom: 12px;
    }

    /* 审核区域的样式 */
    #auditSection {
      border-top: 1px solid #e6e6e6;
      padding-top: 15px;
    }
  </style>
</head>

<body>
  <div class="container">
    <!-- 基本信息 -->
    <div class="section">
      <div class="section-title">基本信息</div>
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">NC客商名称：</span>
          <span class="info-value">中国邮政集团有限公司福州市分公司</span>
        </div>
        <div class="info-item">
          <span class="info-label">NC编号：</span>
          <span class="info-value">N001616</span>
        </div>
      </div>
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">业务月份：</span>
          <span class="info-value">202503</span>
        </div>
        <div class="info-item">
          <span class="info-label">产品类型：</span>
          <span class="info-value">普货件</span>
        </div>
      </div>
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">对应业务大类：</span>
          <span class="info-value">手续费补贴</span>
        </div>
        <div class="info-item">
          <span class="info-label">业务类型：</span>
          <span class="info-value">手续费补贴</span>
        </div>
      </div>
    </div>

    <!-- 金额信息 -->
    <div class="section">
      <div class="section-title">金额信息</div>
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">业务金额：</span>
          <span class="info-value">10000.00</span>
        </div>
        <div class="info-item">
          <span class="info-label">调整金额：</span>
          <span class="info-value">-1000.00</span>
        </div>
      </div>
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">扣印花金额：</span>
          <span class="info-value">1000.00</span>
        </div>
        <div class="info-item">
          <span class="info-label">已开票金额：</span>
          <span class="info-value">1000.00</span>
        </div>
      </div>
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">已回款金额：</span>
          <span class="info-value">1000.00</span>
        </div>
        <div class="info-item">
          <span class="info-label">可拆账金额：</span>
          <span class="info-value red-text">7000.00</span>
        </div>
      </div>

    </div>
  </div>

  <!-- 操作按钮 -->
  <div class="action-buttons">
    <button type="button" class="layui-btn layui-btn-normal" id="splitApply">拆账申请</button>
    <button type="button" class="layui-btn layui-btn-warm" id="splitAudit">拆账审核</button>
    <button type="button" class="layui-btn" id="reSplit">重新拆账</button>
  </div>
  </div>

  <!-- 弹窗模板 -->
  <div id="splitDialog" style="display: none;">
    <!-- 基本信息区域 -->
    <div class="dialog-section">
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">NC客商名称：</span>
          <span class="info-value" data-field="ncCustomerName">中国邮政集团有限公司福州市分公司</span>
        </div>
        <div class="info-item">
          <span class="info-label">NC编号：</span>
          <span class="info-value" data-field="ncCode">N001616</span>
        </div>
      </div>
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">业务月份：</span>
          <span class="info-value" data-field="businessMonth">202503</span>
        </div>
        <div class="info-item">
          <span class="info-label">产品类型：</span>
          <span class="info-value" data-field="productType">普货件</span>
        </div>
      </div>
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">对应业务大类：</span>
          <span class="info-value" data-field="businessCategory">手续费补贴</span>
        </div>
        <div class="info-item">
          <span class="info-label">业务类型：</span>
          <span class="info-value" data-field="businessType">手续费补贴</span>
        </div>
      </div>
    </div>

    <!-- 金额信息区域 -->
    <div class="dialog-section">
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">业务金额：</span>
          <span class="info-value" data-field="businessAmount">10000.00</span>
        </div>
        <div class="info-item">
          <span class="info-label">调整金额：</span>
          <span class="info-value" data-field="adjustAmount">-1000.00</span>
        </div>
      </div>
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">扣印花金额：</span>
          <span class="info-value" data-field="stampAmount">1000.00</span>
        </div>
        <div class="info-item">
          <span class="info-label">已开票金额：</span>
          <span class="info-value" data-field="invoicedAmount">1000.00</span>
        </div>
      </div>
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">已回款金额：</span>
          <span class="info-value" data-field="receivedAmount">1000.00</span>
        </div>
        <div class="info-item">
          <span class="info-label">可拆账金额：</span>
          <span class="info-value red-text" data-field="splitableAmount">7000.00</span>
        </div>
      </div>
      <div class="info-row" id="rejectReasonRow" style="display: none;">
        <div class="info-item" style="flex: 2;">
          <span class="info-label">回退原因：</span>
          <span class="info-value red-text" data-field="rejectReason">不允许拆账给异地邮政</span>
        </div>
      </div>

      <!-- 拆账明细区域 -->
      <div class="dialog-section">
        <div style="margin-bottom: 10px; font-weight: bold;">拆账明细</div>
        <div class="info-row">
          <div class="info-item">
            <span class="info-label">拆账金额：</span>
            <span class="info-value">7000.00；</span>
          </div>
          <div class="info-item">
            <span class="info-label">拆账金额：</span>
            <span class="info-value">3000.00</span>
          </div>
        </div>

        <table class="layui-table" style="margin-top: 15px;">
          <thead>
            <tr>
              <th style="width: 80px;">序号</th>
              <th>NC客商名称</th>
              <th style="width: 120px;">NC编号</th>
              <th style="width: 120px;">拆账金额</th>
              <th id="actionColumn" style="width: 80px; display: none;">操作</th>
            </tr>
          </thead>
          <tbody id="splitTableBody">
            <tr>
              <td>1</td>
              <td>
                <span class="readonly-text">中国邮政集团有限公司三明分公司</span>
                <select class="editable-select layui-hide" name="ncName1" lay-filter="ncName">
                  <option value="">请选择</option>
                  <option value="中国邮政集团有限公司三明分公司" selected>中国邮政集团有限公司三明分公司</option>
                </select>
              </td>
              <td>
                <span class="readonly-text">N1224</span>
                <input type="text" class="editable-input layui-input layui-hide" name="ncCode1" value="N1224">
              </td>
              <td>
                <span class="readonly-text">2000</span>
                <input type="text" class="editable-input layui-input layui-hide" name="amount1" value="2000">
              </td>
              <td class="action-cell" style="display: none;">
                <button type="button" class="layui-btn layui-btn-xs layui-btn-danger delete-row">删除</button>
              </td>
            </tr>
            <tr>
              <td>2</td>
              <td>
                <span class="readonly-text">中国邮政集团有限公司三明分公司</span>
                <select class="editable-select layui-hide" name="ncName2" lay-filter="ncName">
                  <option value="">请选择</option>
                  <option value="中国邮政集团有限公司三明分公司" selected>中国邮政集团有限公司三明分公司</option>
                </select>
              </td>
              <td>
                <span class="readonly-text">N1225</span>
                <input type="text" class="editable-input layui-input layui-hide" name="ncCode2" value="N1225">
              </td>
              <td>
                <span class="readonly-text">5000</span>
                <input type="text" class="editable-input layui-input layui-hide" name="amount2" value="5000">
              </td>
              <td class="action-cell" style="display: none;">
                <button type="button" class="layui-btn layui-btn-xs layui-btn-danger delete-row">删除</button>
              </td>
            </tr>
          </tbody>
        </table>

        <div id="addRowBtn" class="add-row" style="margin-top: 10px; display: none;">
          <a href="javascript:;" style="color: #1E9FFF;">+新增</a>
        </div>
      </div>

      <!-- 审核区域 -->
      <div id="auditSection" class="dialog-section" style="display: none;">
        <div style="margin-bottom: 15px;">
          <span class="info-label">审核结果：</span>
          <select name="auditResult" lay-filter="auditResult" style="width: 200px;">
            <option value="">请选择审核结果</option>
            <option value="pass">审核通过</option>
            <option value="reject">审核拒绝</option>
          </select>
        </div>
        <div>
          <span class="info-label" style="vertical-align: top;">审核原因：</span>
          <textarea name="auditReason" placeholder="请输入审核原因" class="layui-textarea"
            style="width: 400px; height: 100px; resize: vertical;"></textarea>
        </div>
      </div>
    </div>

    <script src="https://unpkg.com/layui@2.8.18/dist/layui.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
      layui.use(['layer', 'form'], function () {
        var layer = layui.layer;
        var form = layui.form;

        // 全局数据存储
        var globalData = {
          detailInfo: null,           // 详情数据
          ncCustomerList: [],         // NC客商列表
          currentDialogType: null,    // 当前弹窗类型
          isDialogOpening: false      // 防止重复打开弹窗
        };

        // 清理全局状态
        function cleanupGlobalState () {
          globalData.currentDialogType = null;
          globalData.isDialogOpening = false;
          // 注意：不清理detailInfo和ncCustomerList，因为这些数据可以复用
        }

        // API接口配置
        var apiConfig = {
          // 获取详情数据接口
          getDetailInfo: {
            url: '/api/splitAccount/getDetailInfo',  // 待确定
            method: 'POST'
          },
          // 获取NC客商列表接口
          getNcCustomerList: {
            url: '/api/splitAccount/getNcCustomerList',  // 待确定
            method: 'POST'
          }
        };

        // 获取详情数据
        function getDetailInfo (params) {
          return new Promise(function (resolve, reject) {
            // 模拟数据（当接口不可用时使用）
            var mockData = {
              ncCustomerName: '中国邮政集团有限公司福州市分公司',
              ncCode: 'N001616',
              businessMonth: '202503',
              productType: '普货件',
              businessCategory: '手续费补贴',
              businessType: '手续费补贴',
              businessAmount: '10000.00',
              adjustAmount: '-1000.00',
              stampAmount: '1000.00',
              invoicedAmount: '1000.00',
              receivedAmount: '1000.00',
              splitableAmount: '7000.00',
              rejectReason: '不允许拆账给异地邮政',
              splitDetails: [
                {
                  ncCustomerName: '中国邮政集团有限公司三明分公司',
                  ncCode: 'N1224',
                  splitAmount: '2000'
                },
                {
                  ncCustomerName: '中国邮政集团有限公司三明分公司',
                  ncCode: 'N1225',
                  splitAmount: '5000'
                }
              ]
            };

            $.ajax({
              url: apiConfig.getDetailInfo.url,
              type: apiConfig.getDetailInfo.method,
              contentType: 'application/json',
              data: JSON.stringify(params || {}),
              success: function (res) {
                if (res.code === 200) {
                  globalData.detailInfo = res.data;
                  resolve(res.data);
                } else {
                  layer.msg('获取详情数据失败: ' + res.msg, { icon: 2 });
                  reject(res);
                }
              },
              error: function (xhr, status, error) {
                // 接口调用失败时使用模拟数据
                console.warn('详情接口调用失败，使用模拟数据');
                globalData.detailInfo = mockData;
                resolve(mockData);
              }
            });
          });
        }

        // 获取NC客商列表
        function getNcCustomerList (params) {
          return new Promise(function (resolve, reject) {
            // 模拟数据（当接口不可用时使用）
            var mockData = [
              {
                ncCode: 'N1224',
                ncCustomerName: '中国邮政集团有限公司三明分公司'
              },
              {
                ncCode: 'N1225',
                ncCustomerName: '中国邮政集团有限公司厦门分公司'
              },
              {
                ncCode: 'N1226',
                ncCustomerName: '中国邮政集团有限公司泉州分公司'
              },
              {
                ncCode: 'N1227',
                ncCustomerName: '中国邮政集团有限公司漳州分公司'
              }
            ];

            $.ajax({
              url: apiConfig.getNcCustomerList.url,
              type: apiConfig.getNcCustomerList.method,
              contentType: 'application/json',
              data: JSON.stringify(params || {}),
              success: function (res) {
                if (res.code === 200) {
                  globalData.ncCustomerList = res.data || [];
                  resolve(res.data);
                } else {
                  layer.msg('获取NC客户数据失败: ' + res.msg, { icon: 2 });
                  reject(res);
                }
              },
              error: function (xhr, status, error) {
                // 接口调用失败时使用模拟数据
                console.warn('NC客商列表接口调用失败，使用模拟数据');
                globalData.ncCustomerList = mockData;
                resolve(mockData);
              }
            });
          });
        }

        // 弹窗配置
        var dialogConfig = {
          apply: {
            title: '拆账申请',
            readonly: false,
            showAudit: false,
            showReject: false,
            showActions: true
          },
          audit: {
            title: '拆账审核',
            readonly: true,
            showAudit: true,
            showReject: false,
            showActions: false
          },
          resplit: {
            title: '重新拆账',
            readonly: false,
            showAudit: false,
            showReject: true,
            showActions: true
          }
        };

        // 通用弹窗函数
        function openSplitDialog (type) {
          // 防止重复打开
          if (globalData.isDialogOpening) {
            return;
          }

          globalData.isDialogOpening = true;
          var config = dialogConfig[type];

          // 强制关闭所有现有弹窗并等待
          layer.closeAll();

          // 短暂延迟确保弹窗完全关闭
          setTimeout(function () {
            // 清理状态
            cleanupGlobalState();
            globalData.currentDialogType = type;
            globalData.isDialogOpening = true;

            // 显示加载层
            var loadingIndex = layer.load(1, {
              shade: [0.3, '#000']
            });

            // 加载数据（使用模拟数据，避免异步问题）
            loadDataAndOpenDialog();

            function loadDataAndOpenDialog () {
              // 关闭加载层
              layer.close(loadingIndex);

              // 使用模拟数据
              if (!globalData.detailInfo) {
                globalData.detailInfo = {
                  ncCustomerName: '中国邮政集团有限公司福州市分公司',
                  ncCode: 'N001616',
                  businessMonth: '202503',
                  productType: '普货件',
                  businessCategory: '手续费补贴',
                  businessType: '手续费补贴',
                  businessAmount: '10000.00',
                  adjustAmount: '-1000.00',
                  stampAmount: '1000.00',
                  invoicedAmount: '1000.00',
                  receivedAmount: '1000.00',
                  splitableAmount: '7000.00',
                  rejectReason: '不允许拆账给异地邮政',
                  splitDetails: [
                    { ncCustomerName: '中国邮政集团有限公司三明分公司', ncCode: 'N1224', splitAmount: '2000' },
                    { ncCustomerName: '中国邮政集团有限公司三明分公司', ncCode: 'N1225', splitAmount: '5000' }
                  ]
                };
              }

              if (globalData.ncCustomerList.length === 0) {
                globalData.ncCustomerList = [
                  { ncCode: 'N1224', ncCustomerName: '中国邮政集团有限公司三明分公司' },
                  { ncCode: 'N1225', ncCustomerName: '中国邮政集团有限公司厦门分公司' },
                  { ncCode: 'N1226', ncCustomerName: '中国邮政集团有限公司泉州分公司' }
                ];
              }

              // 直接打开弹窗
              openDialogWithData(config, type);
            }
          }, 100);
        }

        // 打开弹窗的具体实现
        function openDialogWithData (config, type) {
          var dialogContent = '<div style="padding: 20px;">' + document.getElementById('splitDialog').innerHTML + '</div>';

          var dialogIndex = layer.open({
            type: 1,
            title: config.title,
            area: ['800px', '800px'],
            content: dialogContent,
            btn: ['取消', '确认'],
            yes: function (index) {
              // 取消按钮
              layer.close(index);
              return false; // 阻止默认行为
            },
            btn2: function (index) {
              // 确认按钮
              var layero = $('#layui-layer' + index);
              var success = false;

              if (type === 'audit') {
                success = handleAuditSubmit(layero);
              } else {
                success = handleFormSubmit(layero, type);
              }

              if (success) {
                layer.close(index);
              }
              return false; // 阻止默认行为
            },
            cancel: function (index) {
              // X按钮
              return true;
            },
            end: function () {
              // 弹窗关闭后清理
              cleanupGlobalState();
            },
            success: function (layero, index) {
              // 填充数据
              populateDialogData(layero, globalData.detailInfo);
              // 设置模式
              setDialogModeInLayer(layero, config);
              // 绑定事件
              bindDialogEventsSimple(layero);
              // 渲染表单
              form.render();
              // 标记弹窗打开完成
              globalData.isDialogOpening = false;
            }
          });
        }

        // 填充弹窗数据
        function populateDialogData (layero, data) {
          if (!data) return;

          var layerContent = layero[0];

          // 填充基本信息（示例数据结构，根据实际API返回调整）
          var infoFields = {
            'ncCustomerName': data.ncCustomerName || '中国邮政集团有限公司福州市分公司',
            'ncCode': data.ncCode || 'N001616',
            'businessMonth': data.businessMonth || '202503',
            'productType': data.productType || '普货件',
            'businessCategory': data.businessCategory || '手续费补贴',
            'businessType': data.businessType || '手续费补贴',
            'businessAmount': data.businessAmount || '10000.00',
            'adjustAmount': data.adjustAmount || '-1000.00',
            'stampAmount': data.stampAmount || '1000.00',
            'invoicedAmount': data.invoicedAmount || '1000.00',
            'receivedAmount': data.receivedAmount || '1000.00',
            'splitableAmount': data.splitableAmount || '7000.00',
            'rejectReason': data.rejectReason || '不允许拆账给异地邮政'
          };

          // 更新显示的文本内容
          Object.keys(infoFields).forEach(function (key) {
            var elements = layerContent.querySelectorAll('[data-field="' + key + '"]');
            elements.forEach(function (el) {
              el.textContent = infoFields[key];
            });
          });

          // 填充拆账明细表格
          if (data.splitDetails && data.splitDetails.length > 0) {
            populateSplitTable(layero, data.splitDetails);
          }

          // 更新NC客商下拉框选项
          updateNcCustomerOptions(layero);
        }

        // 填充拆账明细表格
        function populateSplitTable (layero, splitDetails) {
          var tbody = layero[0].querySelector('#splitTableBody');
          if (!tbody) return;

          // 清空现有行
          tbody.innerHTML = '';

          // 添加数据行
          splitDetails.forEach(function (detail, index) {
            var row = createSplitTableRow(index + 1, detail);
            tbody.appendChild(row);
          });
        }

        // 创建拆账明细表格行
        function createSplitTableRow (index, data) {
          var row = document.createElement('tr');
          data = data || {};

          row.innerHTML = `
            <td>${index}</td>
            <td>
              <span class="readonly-text">${data.ncCustomerName || ''}</span>
              <select class="editable-select layui-hide" name="ncName${index}" lay-filter="ncName">
                <option value="">请选择</option>
              </select>
            </td>
            <td>
              <span class="readonly-text">${data.ncCode || ''}</span>
              <input type="text" class="editable-input layui-input layui-hide" name="ncCode${index}" value="${data.ncCode || ''}">
            </td>
            <td>
              <span class="readonly-text">${data.splitAmount || ''}</span>
              <input type="text" class="editable-input layui-input layui-hide" name="amount${index}" value="${data.splitAmount || ''}">
            </td>
            <td class="action-cell" style="display: none;">
              <button type="button" class="layui-btn layui-btn-xs layui-btn-danger delete-row">删除</button>
            </td>
          `;

          return row;
        }

        // 更新NC客商下拉框选项
        function updateNcCustomerOptions (layero) {
          var selects = layero[0].querySelectorAll('select[name^="ncName"]');

          selects.forEach(function (select) {
            // 保存当前选中值
            var currentValue = select.value;

            // 清空选项
            select.innerHTML = '<option value="">请选择</option>';

            // 添加新选项
            globalData.ncCustomerList.forEach(function (customer) {
              var option = document.createElement('option');
              option.value = customer.ncCode || customer.value;
              option.textContent = customer.ncCustomerName || customer.text;
              if (option.value === currentValue) {
                option.selected = true;
              }
              select.appendChild(option);
            });
          });
        }

        // 处理审核提交
        function handleAuditSubmit (layero) {
          var layerContent = layero[0];
          var auditResult = layerContent.querySelector('select[name="auditResult"]').value;
          var auditReason = layerContent.querySelector('textarea[name="auditReason"]').value;

          if (!auditResult) {
            layer.msg('请选择审核结果', { icon: 2 });
            return false;
          }

          var submitData = {
            auditResult: auditResult,
            auditReason: auditReason,
            // 其他需要提交的数据
          };

          // TODO: 调用审核提交接口
          console.log('审核提交数据:', submitData);
          layer.msg('审核完成');
          return true;
        }

        // 处理表单提交
        function handleFormSubmit (layero, type) {
          var layerContent = layero[0];

          // 收集拆账明细数据
          var splitDetails = [];
          var rows = layerContent.querySelectorAll('#splitTableBody tr');

          rows.forEach(function (row, index) {
            var ncNameSelect = row.querySelector('select[name^="ncName"]');
            var ncCodeInput = row.querySelector('input[name^="ncCode"]');
            var amountInput = row.querySelector('input[name^="amount"]');

            if (ncNameSelect && ncCodeInput && amountInput) {
              splitDetails.push({
                ncCustomerName: ncNameSelect.options[ncNameSelect.selectedIndex].text,
                ncCode: ncCodeInput.value,
                splitAmount: amountInput.value
              });
            }
          });

          var submitData = {
            type: type,
            splitDetails: splitDetails,
            // 其他需要提交的数据
          };

          // TODO: 调用提交接口
          console.log('表单提交数据:', submitData);
          layer.msg('操作完成');
          return true;
        }

        // 在弹窗层中设置模式
        function setDialogModeInLayer (layero, config) {
          var layerContent = layero[0];
          var auditSection = layerContent.querySelector('#auditSection');
          var rejectReasonRow = layerContent.querySelector('#rejectReasonRow');
          var addRowBtn = layerContent.querySelector('#addRowBtn');
          var actionColumn = layerContent.querySelector('#actionColumn');
          var actionCells = layerContent.querySelectorAll('.action-cell');
          var readonlyTexts = layerContent.querySelectorAll('.readonly-text');
          var editableInputs = layerContent.querySelectorAll('.editable-input');
          var editableSelects = layerContent.querySelectorAll('.editable-select');

          // 显示/隐藏审核区域
          if (auditSection) {
            auditSection.style.display = config.showAudit ? 'block' : 'none';
          }

          // 显示/隐藏回退原因
          if (rejectReasonRow) {
            rejectReasonRow.style.display = config.showReject ? 'block' : 'none';
          }

          // 显示/隐藏新增按钮
          if (addRowBtn) {
            addRowBtn.style.display = config.showActions ? 'block' : 'none';
          }

          // 显示/隐藏操作列
          if (actionColumn) {
            actionColumn.style.display = config.showActions ? 'table-cell' : 'none';
          }
          for (var i = 0; i < actionCells.length; i++) {
            actionCells[i].style.display = config.showActions ? 'table-cell' : 'none';
          }

          // 切换只读/编辑模式
          if (config.readonly) {
            for (var i = 0; i < readonlyTexts.length; i++) {
              readonlyTexts[i].style.display = 'inline-block';
            }
            for (var i = 0; i < editableInputs.length; i++) {
              editableInputs[i].classList.add('layui-hide');
            }
            for (var i = 0; i < editableSelects.length; i++) {
              editableSelects[i].classList.add('layui-hide');
            }
          } else {
            for (var i = 0; i < readonlyTexts.length; i++) {
              readonlyTexts[i].style.display = 'none';
            }
            for (var i = 0; i < editableInputs.length; i++) {
              editableInputs[i].classList.remove('layui-hide');
            }
            for (var i = 0; i < editableSelects.length; i++) {
              editableSelects[i].classList.remove('layui-hide');
            }
          }
        }

        // 简化的事件绑定函数
        function bindDialogEventsSimple (layero) {
          // 移除所有可能的事件监听器
          layero.off('click.dialogEvent');

          // 使用事件委托绑定删除按钮事件
          layero.on('click.dialogEvent', '.delete-row', function (e) {
            e.preventDefault();
            e.stopPropagation();
            deleteRowSimple(this, layero);
          });

          // 绑定新增按钮事件
          layero.on('click.dialogEvent', '#addRowBtn a', function (e) {
            e.preventDefault();
            e.stopPropagation();
            addRowSimple(layero);
          });
        }

        // 简化的添加行函数
        function addRowSimple (layero) {
          var tbody = layero.find('#splitTableBody')[0];
          var rowCount = tbody.children.length + 1;
          var newRow = document.createElement('tr');

          newRow.innerHTML =
            '<td>' + rowCount + '</td>' +
            '<td>' +
            '<span class="readonly-text" style="display: none;">中国邮政集团有限公司三明分公司</span>' +
            '<select class="editable-select" name="ncName' + rowCount + '" lay-filter="ncName">' +
            '<option value="">请选择</option>' +
            '<option value="中国邮政集团有限公司三明分公司">中国邮政集团有限公司三明分公司</option>' +
            '</select>' +
            '</td>' +
            '<td>' +
            '<span class="readonly-text" style="display: none;"></span>' +
            '<input type="text" class="editable-input layui-input" name="ncCode' + rowCount + '">' +
            '</td>' +
            '<td>' +
            '<span class="readonly-text" style="display: none;"></span>' +
            '<input type="text" class="editable-input layui-input" name="amount' + rowCount + '">' +
            '</td>' +
            '<td class="action-cell">' +
            '<button type="button" class="layui-btn layui-btn-xs layui-btn-danger delete-row">删除</button>' +
            '</td>';

          tbody.appendChild(newRow);
          form.render('select');
        }

        // 简化的删除行函数
        function deleteRowSimple (btn, layero) {
          var tbody = layero.find('#splitTableBody')[0];
          var rows = tbody.children;

          // 检查是否只剩一行
          if (rows.length <= 1) {
            layer.msg('至少需要保留一条拆账明细记录', { icon: 2 });
            return;
          }

          var row = btn.closest('tr');
          row.remove();

          // 重新编号
          var updatedRows = tbody.children;
          for (var i = 0; i < updatedRows.length; i++) {
            updatedRows[i].children[0].textContent = i + 1;
          }
        }

        // 绑定主页面按钮事件
        document.getElementById('splitApply').addEventListener('click', function () {
          openSplitDialog('apply');
        });

        document.getElementById('splitAudit').addEventListener('click', function () {
          openSplitDialog('audit');
        });

        document.getElementById('reSplit').addEventListener('click', function () {
          openSplitDialog('resplit');
        });
      });
    </script>
</body>

</html>