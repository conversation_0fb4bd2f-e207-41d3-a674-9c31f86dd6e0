<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <title>费用业务运费拆账</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <link rel="stylesheet" href="https://unpkg.com/layui@2.8.18/dist/css/layui.css">
  <style>
    .container {
      padding: 20px;
      max-width: 1200px;
      margin: 0 auto;
    }

    .section {
      margin-bottom: 30px;
      border: 1px solid #e6e6e6;
      border-radius: 5px;
      padding: 20px;
    }

    .section-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 15px;
      color: #333;
    }

    .info-row {
      display: flex;
      margin-bottom: 10px;
    }

    .info-item {
      flex: 1;
      margin-right: 20px;
    }

    .info-item:last-child {
      margin-right: 0;
    }

    .info-label {
      display: inline-block;
      width: 100px;
      color: #666;
    }

    .info-value {
      color: #333;
    }

    .red-text {
      color: #ff0000;
    }

    .action-buttons {
      text-align: center;
      margin-top: 20px;
    }

    .action-buttons .layui-btn {
      margin: 0 10px;
    }

    .split-table {
      margin-top: 15px;
    }

    .add-row {
      color: #1E9FFF;
      cursor: pointer;
      margin-top: 10px;
    }

    .add-row:hover {
      text-decoration: underline;
    }

    .dialog-section {
      margin-bottom: 20px;
    }

    .readonly-text {
      display: inline-block;
    }

    .editable-input,
    .editable-select {
      width: 100%;
    }

    /* 弹窗内容样式 */
    #splitDialog {
      padding: 20px !important;
    }

    /* 确保弹窗内的表格有适当间距 */
    #splitDialog .layui-table {
      margin: 15px 0;
    }

    /* 弹窗内信息行的间距 */
    #splitDialog .info-row {
      margin-bottom: 12px;
    }

    /* 审核区域的样式 */
    #auditSection {
      border-top: 1px solid #e6e6e6;
      padding-top: 15px;
    }
  </style>
</head>

<body>
  <div class="container">
    <!-- 基本信息 -->
    <div class="section">
      <div class="section-title">基本信息</div>
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">NC客商名称：</span>
          <span class="info-value">中国邮政集团有限公司福州市分公司</span>
        </div>
        <div class="info-item">
          <span class="info-label">NC编号：</span>
          <span class="info-value">N001616</span>
        </div>
      </div>
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">业务月份：</span>
          <span class="info-value">202503</span>
        </div>
        <div class="info-item">
          <span class="info-label">产品类型：</span>
          <span class="info-value">普货件</span>
        </div>
      </div>
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">对应业务大类：</span>
          <span class="info-value">手续费补贴</span>
        </div>
        <div class="info-item">
          <span class="info-label">业务类型：</span>
          <span class="info-value">手续费补贴</span>
        </div>
      </div>
    </div>

    <!-- 金额信息 -->
    <div class="section">
      <div class="section-title">金额信息</div>
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">业务金额：</span>
          <span class="info-value">10000.00</span>
        </div>
        <div class="info-item">
          <span class="info-label">调整金额：</span>
          <span class="info-value">-1000.00</span>
        </div>
      </div>
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">扣印花金额：</span>
          <span class="info-value">1000.00</span>
        </div>
        <div class="info-item">
          <span class="info-label">已开票金额：</span>
          <span class="info-value">1000.00</span>
        </div>
      </div>
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">已回款金额：</span>
          <span class="info-value">1000.00</span>
        </div>
        <div class="info-item">
          <span class="info-label">可拆账金额：</span>
          <span class="info-value red-text">7000.00</span>
        </div>
      </div>
      <div class="info-row" id="rejectReasonRow" style="display: none;">
        <div class="info-item" style="flex: 2;">
          <span class="info-label">回退原因：</span>
          <span class="info-value red-text">不允许拆账给异地邮政</span>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <button type="button" class="layui-btn layui-btn-normal" id="splitApply">拆账申请</button>
      <button type="button" class="layui-btn layui-btn-warm" id="splitAudit">拆账审核</button>
      <button type="button" class="layui-btn" id="reSplit">重新拆账</button>
    </div>
  </div>

  <!-- 弹窗模板 -->
  <div id="splitDialog" style="display: none;">
    <!-- 基本信息区域 -->
    <div class="dialog-section">
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">NC客商名称：</span>
          <span class="info-value">中国邮政集团有限公司福州市分公司</span>
        </div>
        <div class="info-item">
          <span class="info-label">NC编号：</span>
          <span class="info-value">N001616</span>
        </div>
      </div>
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">业务月份：</span>
          <span class="info-value">202503</span>
        </div>
        <div class="info-item">
          <span class="info-label">产品类型：</span>
          <span class="info-value">普货件</span>
        </div>
      </div>
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">对应业务大类：</span>
          <span class="info-value">手续费补贴</span>
        </div>
        <div class="info-item">
          <span class="info-label">业务类型：</span>
          <span class="info-value">手续费补贴</span>
        </div>
      </div>
    </div>

    <!-- 金额信息区域 -->
    <div class="dialog-section">
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">业务金额：</span>
          <span class="info-value">10000.00</span>
        </div>
        <div class="info-item">
          <span class="info-label">调整金额：</span>
          <span class="info-value">-1000.00</span>
        </div>
      </div>
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">扣印花金额：</span>
          <span class="info-value">1000.00</span>
        </div>
        <div class="info-item">
          <span class="info-label">已开票金额：</span>
          <span class="info-value">1000.00</span>
        </div>
      </div>
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">已回款金额：</span>
          <span class="info-value">1000.00</span>
        </div>
        <div class="info-item">
          <span class="info-label">可拆账金额：</span>
          <span class="info-value red-text">7000.00</span>
        </div>
      </div>
    </div>

    <!-- 拆账明细区域 -->
    <div class="dialog-section">
      <div style="margin-bottom: 10px; font-weight: bold;">拆账明细</div>
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">拆账金额：</span>
          <span class="info-value">7000.00；</span>
        </div>
        <div class="info-item">
          <span class="info-label">拆账金额：</span>
          <span class="info-value">3000.00</span>
        </div>
      </div>

      <table class="layui-table" style="margin-top: 15px;">
        <thead>
          <tr>
            <th style="width: 80px;">序号</th>
            <th>NC客商名称</th>
            <th style="width: 120px;">NC编号</th>
            <th style="width: 120px;">拆账金额</th>
            <th id="actionColumn" style="width: 80px; display: none;">操作</th>
          </tr>
        </thead>
        <tbody id="splitTableBody">
          <tr>
            <td>1</td>
            <td>
              <span class="readonly-text">中国邮政集团有限公司三明分公司</span>
              <select class="editable-select layui-hide" name="ncName1" lay-filter="ncName">
                <option value="">请选择</option>
                <option value="中国邮政集团有限公司三明分公司" selected>中国邮政集团有限公司三明分公司</option>
              </select>
            </td>
            <td>
              <span class="readonly-text">N1224</span>
              <input type="text" class="editable-input layui-input layui-hide" name="ncCode1" value="N1224">
            </td>
            <td>
              <span class="readonly-text">2000</span>
              <input type="text" class="editable-input layui-input layui-hide" name="amount1" value="2000">
            </td>
            <td class="action-cell" style="display: none;">
              <button type="button" class="layui-btn layui-btn-xs layui-btn-danger delete-row">删除</button>
            </td>
          </tr>
          <tr>
            <td>2</td>
            <td>
              <span class="readonly-text">中国邮政集团有限公司三明分公司</span>
              <select class="editable-select layui-hide" name="ncName2" lay-filter="ncName">
                <option value="">请选择</option>
                <option value="中国邮政集团有限公司三明分公司" selected>中国邮政集团有限公司三明分公司</option>
              </select>
            </td>
            <td>
              <span class="readonly-text">N1225</span>
              <input type="text" class="editable-input layui-input layui-hide" name="ncCode2" value="N1225">
            </td>
            <td>
              <span class="readonly-text">5000</span>
              <input type="text" class="editable-input layui-input layui-hide" name="amount2" value="5000">
            </td>
            <td class="action-cell" style="display: none;">
              <button type="button" class="layui-btn layui-btn-xs layui-btn-danger delete-row">删除</button>
            </td>
          </tr>
        </tbody>
      </table>

      <div id="addRowBtn" class="add-row" style="margin-top: 10px; display: none;">
        <a href="javascript:;" style="color: #1E9FFF;">+新增</a>
      </div>
    </div>

    <!-- 审核区域 -->
    <div id="auditSection" class="dialog-section" style="display: none;">
      <div style="margin-bottom: 15px;">
        <span class="info-label">审核结果：</span>
        <select name="auditResult" lay-filter="auditResult" style="width: 200px;">
          <option value="">请选择审核结果</option>
          <option value="pass">审核通过</option>
          <option value="reject">审核拒绝</option>
        </select>
      </div>
      <div>
        <span class="info-label" style="vertical-align: top;">审核原因：</span>
        <textarea name="auditReason" placeholder="请输入审核原因" class="layui-textarea"
          style="width: 400px; height: 100px; resize: vertical;"></textarea>
      </div>
    </div>
  </div>

  <script src="https://unpkg.com/layui@2.8.18/dist/layui.js"></script>
  <script>
    layui.use(['layer', 'form'], function () {
      var layer = layui.layer;
      var form = layui.form;

      // 弹窗配置
      var dialogConfig = {
        apply: {
          title: '拆账申请',
          readonly: false,
          showAudit: false,
          showReject: false,
          showActions: true
        },
        audit: {
          title: '拆账审核',
          readonly: true,
          showAudit: true,
          showReject: false,
          showActions: false
        },
        resplit: {
          title: '重新拆账',
          readonly: false,
          showAudit: false,
          showReject: true,
          showActions: true
        }
      };

      // 通用弹窗函数
      function openSplitDialog (type) {
        var config = dialogConfig[type];

        layer.open({
          type: 1,
          title: config.title,
          area: ['800px', '800px'],
          content: '<div style="padding: 20px;">' + document.getElementById('splitDialog').innerHTML + '</div>',
          btn: ['取消', '确认'],
          yes: function (index) {
            layer.close(index);
          },
          btn2: function (index) {
            if (type === 'audit') {
              layer.msg('审核完成');
            } else {
              layer.msg('操作完成');
            }
            layer.close(index);
          },
          success: function (layero, index) {
            // 弹窗打开后设置模式
            setDialogModeInLayer(layero, config);
            // 重新绑定事件
            bindDialogEvents(layero);
            form.render();
          }
        });
      }

      // 在弹窗层中设置模式
      function setDialogModeInLayer (layero, config) {
        var auditSection = layero.find('#auditSection')[0];
        var rejectReasonRow = layero.find('#rejectReasonRow')[0];
        var addRowBtn = layero.find('#addRowBtn')[0];
        var actionColumn = layero.find('#actionColumn')[0];
        var actionCells = layero.find('.action-cell');
        var readonlyTexts = layero.find('.readonly-text');
        var editableInputs = layero.find('.editable-input');
        var editableSelects = layero.find('.editable-select');

        // 显示/隐藏审核区域
        if (auditSection) {
          auditSection.style.display = config.showAudit ? 'block' : 'none';
        }

        // 显示/隐藏回退原因
        if (rejectReasonRow) {
          rejectReasonRow.style.display = config.showReject ? 'block' : 'none';
        }

        // 显示/隐藏新增按钮
        if (addRowBtn) {
          addRowBtn.style.display = config.showActions ? 'block' : 'none';
        }

        // 显示/隐藏操作列
        if (actionColumn) {
          actionColumn.style.display = config.showActions ? 'table-cell' : 'none';
        }
        actionCells.each(function () {
          this.style.display = config.showActions ? 'table-cell' : 'none';
        });

        // 切换只读/编辑模式
        if (config.readonly) {
          readonlyTexts.each(function () {
            this.style.display = 'inline-block';
          });
          editableInputs.each(function () {
            this.classList.add('layui-hide');
          });
          editableSelects.each(function () {
            this.classList.add('layui-hide');
          });
        } else {
          readonlyTexts.each(function () {
            this.style.display = 'none';
          });
          editableInputs.each(function () {
            this.classList.remove('layui-hide');
          });
          editableSelects.each(function () {
            this.classList.remove('layui-hide');
          });
        }
      }

      // 绑定弹窗内事件
      function bindDialogEvents (layero) {
        var addBtn = layero.find('#addRowBtn a')[0];
        var deleteButtons = layero.find('.delete-row');

        // 绑定新增按钮事件
        if (addBtn) {
          addBtn.addEventListener('click', function () {
            addRow(layero);
          });
        }

        // 绑定删除按钮事件
        deleteButtons.each(function () {
          this.addEventListener('click', function () {
            deleteRow(this, layero);
          });
        });
      }

      // 添加行
      function addRow (layero) {
        var tbody = layero.find('#splitTableBody')[0];
        var rowCount = tbody.children.length + 1;
        var newRow = document.createElement('tr');

        newRow.innerHTML = `
          <td>${rowCount}</td>
          <td>
            <span class="readonly-text" style="display: none;">中国邮政集团有限公司三明分公司</span>
            <select class="editable-select" name="ncName${rowCount}" lay-filter="ncName">
              <option value="">请选择</option>
              <option value="中国邮政集团有限公司三明分公司">中国邮政集团有限公司三明分公司</option>
            </select>
          </td>
          <td>
            <span class="readonly-text" style="display: none;"></span>
            <input type="text" class="editable-input layui-input" name="ncCode${rowCount}">
          </td>
          <td>
            <span class="readonly-text" style="display: none;"></span>
            <input type="text" class="editable-input layui-input" name="amount${rowCount}">
          </td>
          <td class="action-cell">
            <button type="button" class="layui-btn layui-btn-xs layui-btn-danger delete-row">删除</button>
          </td>
        `;

        tbody.appendChild(newRow);

        // 绑定新行的删除事件
        var deleteBtn = newRow.querySelector('.delete-row');
        deleteBtn.addEventListener('click', function () {
          deleteRow(this, layero);
        });

        form.render('select');
      }

      // 删除行
      function deleteRow (btn, layero) {
        var tbody = layero.find('#splitTableBody')[0];
        var rows = tbody.children;

        // 检查是否只剩一行，如果是则不允许删除
        if (rows.length <= 1) {
          layer.msg('至少需要保留一条拆账明细记录', { icon: 2 });
          return;
        }

        var row = btn.closest('tr');
        row.remove();

        // 重新编号
        var updatedRows = tbody.children;
        for (var i = 0; i < updatedRows.length; i++) {
          updatedRows[i].children[0].textContent = i + 1;
        }
      }

      // 绑定主页面按钮事件
      document.getElementById('splitApply').addEventListener('click', function () {
        openSplitDialog('apply');
      });

      document.getElementById('splitAudit').addEventListener('click', function () {
        openSplitDialog('audit');
      });

      document.getElementById('reSplit').addEventListener('click', function () {
        openSplitDialog('resplit');
      });
    });
  </script>
</body>

</html>