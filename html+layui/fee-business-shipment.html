<!DOCTYPE html>
<html lang="zh">
<html class="x-admin-sm">
<head>
    <meta http-equiv="Expires" content="0">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Cache-control" content="no-cache">
    <meta http-equiv="Cache" content="no-cache">
    <meta charset="UTF-8">
    <title>手续费业务明细-列表</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport"
          content="width=device-width,user-scalable=yes, minimum-scale=0.4, initial-scale=0.8,target-densitydpi=low-dpi"/>
    <link rel="stylesheet" href="../../css/font.css">
    <link rel="stylesheet" href="../../css/xadmin.css">
    <link rel="stylesheet" href="../../css/layui.css">
    <script src="../../lib/layui/layui.js" charset="utf-8"></script>
    <script type="text/javascript" src="../../js/xadmin.js"></script>
    <script type="text/javascript" src="../../js/jquery-3.4.1.min.js"></script>
    <script type="text/javascript" src="../../js/axios.js"></script>
    <script type="text/javascript" src="../../js/toolFunction.js"></script>
    <script type="text/javascript" src="../js/business_detail.js"></script>
    <script type="text/javascript" src="../../js/form.js"></script>
    <script type="stylesheet" src="../../js/multiple.css"></script>
    <script type="text/javascript" src="../js/bussiness-active-type.js"></script>
    <script type="text/javascript" src="../../js/xm-select.js"></script>
    <style>
        .layui-table td, .layui-table th {
            min-width: 40px;
        }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-card-body">
                <form class="layui-form " id="selectDiv">
                    <div class="layui-row">
                        <div class="layui-col-md3" style="margin-top: 3px">
                            <label class="layui-form-label" style="margin-top: -5px;width: 100px">业务月份</label>
                            <div class="layui-input-inline" style="width: 87px">
                                <input class="layui-input" name="businessMonthStart" id="businessMonthStart" placeholder="业务月份起">
                            </div>
                            -
                            <div class="layui-input-inline" style="width: 87px">
                                <input class="layui-input" name="businessMonthEnd" id="businessMonthEnd" placeholder="业务月份止">
                            </div>

                        </div>
                        <div class="layui-col-md3" style="margin-top: 3px">
                            <label class="layui-form-label" style="margin-top: -5px;width: 100px">备注</label>
                            <div class="layui-input-inline" style="width: 50%">
                                <input class="layui-input" id="remarks" name="remarks" placeholder="请输入备注"/>
                            </div>
                        </div>
                        <div class="layui-col-md3" style="margin-top: 3px">
                            <label class="layui-form-label" style="margin-top: -5px;width: 100px">业务流水号</label>
                            <div class="layui-input-inline" style="width: 50%">
                                <input class="layui-input" id="shipmentId" name="shipmentId" placeholder="请输入业务流水号"/>
                            </div>
                        </div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-md3" style="margin-top: 3px">
                            <label class="layui-form-label" style="margin-top: -5px;width: 100px">NC编号</label>
                            <div class="layui-input-inline" style="width: 50%">
                                <input class="layui-input" id="ncNo" name="ncNo" placeholder="请输入NC编号"/>
                            </div>
                        </div>
                        <div class="layui-col-md3" style="margin-top: 3px">
                            <label class="layui-form-label" style="margin-top: -5px;width: 100px">NC名称</label>
                            <div class="layui-input-inline" style="width: 50%">
                                <input class="layui-input" id="ncName" name="ncName" placeholder="请输入NC名称"/>
                            </div>
                        </div>
                        <div class="layui-col-md3" style="margin-top: 3px">
                            <label class="layui-form-label" style="margin-top: -5px;width: 100px">审核状态</label>
                            <div class="layui-input-inline" style="width: 50%">
                                <select class="layui-input" name="auditStatus" id="auditStatus">
                                    <option value="">请选择</option>
                                    <option value="1">未审核</option>
                                    <option value="4">审核通过</option>
                                    <option value="2">审核拒绝</option>
                                    <option value="3">部分审核通过</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="layui-row">
<!--                        <div class="layui-col-md3" style="margin-top: 3px">-->
<!--                            <label class="layui-form-label" style="margin-top: -5px;width: 100px">应税类型</label>-->
<!--                            <div class="layui-input-inline" style="width: 50%">-->
<!--                                <select class="layui-input" name="taxType" id="taxType">-->
<!--                                    <option value="">请选择</option>-->
<!--                                    <option value="1">销售货物</option>-->
<!--                                    <option value="2">销售劳务</option>-->
<!--                                </select>-->
<!--                            </div>-->
<!--                        </div>-->
                        <div class="layui-col-md3" style="margin-top: 3px">
                            <label class="layui-form-label" style="margin-top: -5px;width: 100px">导入时间</label>
                            <div class="layui-input-inline" style="width: 87px">
                                <input class="layui-input" name="createTimeStart" id="createTimeStart" placeholder="导入时间起">
                            </div>
                            -
                            <div class="layui-input-inline" style="width: 87px">
                                <input class="layui-input" name="createTimeEnd" id="createTimeEnd" placeholder="导入时间止">
                            </div>
                        </div>

                        <div class="layui-col-md3" style="margin-top: 3px">
                            <label class="layui-form-label" style="margin-top: -5px;width: 100px">导入人</label>
                            <div class="layui-input-inline" style="width: 50%">
                                <input class="layui-input" id="createStaff" name="createStaff" placeholder="请输入导入人"/>
                            </div>
                        </div>
                        <div class="layui-col-md3" style="margin-top: 3px">
                            <label class="layui-form-label" style="margin-top: -5px">业务主体</label>
                            <div class="layui-input-inline" style="width: 50%">
                                <select class="businessEntity" name="businessEntity" lay-filter="businessEntity" id="businessEntity">
                                    <option value="">请选择</option>
                                    <option value="5">福建国通星驿网络科技有限公司</option>
                                    <option value="4">福建云势数据科技服务有限公司</option>
                                    <option value="1">上海杉昊智能科技发展有限公司</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-md3" style="margin-top: 3px">
                            <label class="layui-form-label" style="margin-top: -5px">签收状态</label>
                            <div class="layui-input-inline" style="width: 50%">
                                <select class="signStatus" name="signStatus" lay-filter="signStatus"
                                        id="signStatus">
                                    <option value="">请选择</option>
                                    <option value="0">已签收</option>
                                    <option value="1">未签收</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-col-md3" style="margin-top: 3px">
                            <label class="layui-form-label" style="margin-top: -5px">确认状态</label>
                            <div class="layui-input-inline" style="width: 50%">
                                <select class="applyStatus" name="confirmStatus" lay-filter="confirmStatus"
                                        id="confirmStatus">
                                    <option value="">请选择</option>
                                    <option value="0">未确认</option>
                                    <option value="1">已确认</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="moreSearch" hidden>

                        <div class="layui-row">
                            <div class="layui-col-md3" style="margin-top: 3px">
                                <label class="layui-form-label" style="margin-top: -5px;width: 100px">申请人</label>
                                <div class="layui-input-inline" style="width: 50%">
                                    <input class="layui-input" id="applyStaff " name="applyStaff" placeholder="请输入申请人"/>
                                </div>
                            </div>
                            <div class="layui-col-md3" style="margin-top: 3px">
                                <label class="layui-form-label" style="margin-top: -5px;width: 100px">审核人</label>
                                <div class="layui-input-inline" style="width: 50%">
                                    <input class="layui-input" type="text" name="auditStaff" id="auditStaff" placeholder="请输入审核人"/>
                                </div>
                            </div>
                            <div class="layui-col-md3" style="margin-top: 3px">
                                <label class="layui-form-label" style="margin-top: -5px;width: 100px">所属团队</label>
                                <div class="layui-input-inline" style="width: 50%">
                                    <select class="layui-input" name="belongTeam" id="belongTeam">
                                        <option value="">请选择</option>
                                        <option value="1">国通本部</option>
                                        <option value="2">国通互联</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="layui-row">
                            <div class="layui-col-md3" style="margin-top: 3px">
                                <label class="layui-form-label" style="margin-top: -5px;width: 100px">申请状态</label>
                                <div class="layui-input-inline" style="width: 50%">
                                    <select class="layui-input" name="applyStatus" id="applyStatus">
                                        <option value="">请选择</option>
                                        <option value="1">未申请</option>
                                        <option value="2">部分申请</option>
                                        <option value="3">已申请</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-col-md3" style="margin-top: 3px">
                                <label class="layui-form-label" style="margin-top: -5px;width: 100px">可开票金额</label>
                                <div class="layui-input-inline" style="width: 50%">
                                    <select class="layui-input" name="money" id="money">
                                        <option value="">请选择</option>
                                        <option value="1">大于0</option>
                                        <option value="2">0</option>
                                        <option value="3">小于0</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-col-md3" style="margin-top: 3px">
                                <label class="layui-form-label" style="margin-top: -5px;width: 100px">开票申请编号</label>
                                <div class="layui-input-inline" style="width: 50%">
                                    <input class="layui-input" type="text" name="invoiceId" id="invoiceId" placeholder="请输入开票申请编号"/>
                                </div>
                            </div>
                        </div>
                        <div class="layui-row">
                            <div class="layui-col-md3" style="margin-top: 3px">
                                <label class="layui-form-label" style="margin-top: -5px;width: 100px">产品类型</label>
                                <div class="layui-input-inline" style="width: 50%">
                                    <select class="layui-input" name="productType" id="productType">

                                    </select>
                                </div>
                            </div>
                            <div class="layui-col-md3" style="margin-top: 3px">
                                <label class="layui-form-label" style="margin-top: -5px;width: 100px">对应业务大类</label>
                                <div class="layui-input-inline" style="width: 50%">
                                    <select class="layui-input"  lay-filter="bigType" name="bigType" id="bigType">

                                    </select>
                                </div>
                            </div>
                            <div class="layui-col-md3" style="margin-top: 3px">
                                <label class="layui-form-label" style="margin-top: -5px;width: 100px">业务类型</label>
                                <div class="layui-input-inline" style="width: 50%">
                                    <select class="layui-input" name="newbusinessDesc" id="newbusinessDesc">

                                    </select>
                                </div>
                            </div>

                        </div>
                        <div class="layui-row">
                            <div class="layui-col-md3" style="margin-top: 3px">
                                <label class="layui-form-label" style="margin-top: -5px;width: 100px">发票号</label>
                                <div class="layui-input-inline" style="width: 50%">
                                    <input class="layui-input" type="text" name="invoiceNo" id="invoiceNo" placeholder="请输入发票号"/>
                                </div>
                            </div>
                            <div class="layui-col-md3" style="margin-top: 3px">
                                <label class="layui-form-label" style="margin-top: -5px;width: 100px">回款状态</label>
                                <div class="layui-input-inline" style="width: 50%">
                                    <select class="backStatus" name="backStatus" lay-filter="backStatus">
                                        <option value="">全部</option>
                                        <option value="1">已回款</option>
                                        <option value="2">未回款</option>
                                        <option value="3">部分回款</option>
                                    </select>
                                </div>
                            </div>

                            <div class="layui-col-md3" style="margin-top: 3px">

                                <label class="layui-form-label" style="margin-top: -5px;width: 100px">归属月份</label>
                                <div class="layui-input-inline" style="width: 87px">
                                    <input class="layui-input" name="entryMonthQueryStart" id="entryMonthQueryStart" placeholder="归属月份起">
                                </div>
                                -
                                <div class="layui-input-inline" style="width: 87px">
                                    <input class="layui-input" name="entryMonthQueryEnd" id="entryMonthQueryEnd" placeholder="归属月份止">
                                </div>

                            </div>
                        </div>

                        <div class="layui-row">
                            <div class="layui-col-md3" style="margin-top: 3px;margin-left: -51px;display: none">
                                <label class="layui-form-label" style="margin-top: -5px;width: 150px">
                                    省份</label>
                                <!--省份-->
                                <div class="layui-input-inline" style="width:50%">
                                    <select class="layui-input" name="feeBussInProvinceId"
                                            lay-filter="feeBussInProvinceId" id="feeBussInProvinceId">
                                        <option value="">请选择</option>
                                    </select>
                                </div>
                            </div>

                            <div class="layui-col-md3" style="margin-top: 3px;display: none">

                                <label class="layui-form-label" style="margin-top: -5px;width: 150px">
                                    地市</label>
                                <!--地市-->
                                <div class="layui-input-inline" style="width:50%">
                                    <select class="layui-input" name="feeBussInCityId"
                                            lay-filter="feeBussInCityId" id="feeBussInCityId">
                                        <option value="">请选择</option>
                                    </select>
                                </div>

                            </div>

                            <div class="layui-col-md3" style="margin-top: 3px;margin-left: -51px;">
                                <label class="layui-form-label" style="margin-top: -5px;width: 150px">省份</label>
                                <div class="layui-input-inline" style="width:50%">
                                    <div id="sssf" class="xm-select-demo"></div>
                                </div>
                            </div>

                            <div class="layui-col-md3" style="margin-top: 3px;">
                                <label class="layui-form-label" style="margin-top: -5px;width: 150px">地市</label>
                                <div class="layui-input-inline" style="width:50%">
                                    <div id="ssds" class="xm-select-demo"></div>
                                </div>
                            </div>

                            <div class="layui-col-md3" style="margin-top: 3px;margin-left: 52px;">

                                <label class="layui-form-label" style="margin-top: -5px;width: 100px">回款日期</label>
                                <div class="layui-input-inline" style="width: 87px">
                                    <input class="layui-input" name="huiKuanTimeStart" id="huiKuanTimeStart" placeholder="回款日期起">
                                </div>
                                -
                                <div class="layui-input-inline" style="width: 87px">
                                    <input class="layui-input" name="huiKuanTimeEnd" id="huiKuanTimeEnd" placeholder="回款日期止">
                                </div>

                            </div>

                        </div>

                        <div class="layui-row">

                            <div class="layui-col-md3" style="margin-top: 3px">
                                <label class="layui-form-label" style="margin-top: -5px;width: 100px">订单号</label>
                                <div class="layui-input-inline" style="width: 50%">
                                    <input class="layui-input" type="text" name="orderNumberQuery" id="orderNumberQuery" placeholder="请输入订单号"/>
                                </div>
                            </div>


                            <div class="layui-col-md3" style="margin-top: 3px">
                                <label class="layui-form-label" style="margin-top: -5px;width: 100px">营销活动名称</label>
                                <div class="layui-input-inline" style="width: 50%">
                                    <input class="layui-input" type="text" name="marketActivityNameQuery" id="marketActivityNameQuery" placeholder="请输入营销活动名称"/>
                                </div>
                            </div>


                            <div class="layui-col-md3" style="margin-top: 3px">
                                <label class="layui-form-label" style="margin-top: -5px;width: 100px">客户类别</label>
                                <div class="layui-input-inline" style="width: 50%">
                                    <select class="layui-input" name="customerTypeQuery" id="customerTypeQuery">
                                        <option value="">请选择</option>
                                        <option value="商户">商户</option>
                                        <option value="代理">代理</option>
                                        <option value="邮政客户">邮政客户</option>
                                        <option value="银行客户">银行客户</option>
                                    </select>
                                </div>
                            </div>


                        </div>
                        <div class="layui-row">
                            <div class="layui-col-md3" style="margin-top: 3px">

                                <label class="layui-form-label" style="margin-top: -5px;width: 100px">入账月份</label>
                                <div class="layui-input-inline" style="width: 87px">
                                    <input class="layui-input" name="addAccountMonthQueryStart" id="addAccountMonthQueryStart" placeholder="入账月份起">
                                </div>
                                -
                                <div class="layui-input-inline" style="width: 87px">
                                    <input class="layui-input" name="addAccountMonthQueryEnd" id="addAccountMonthQueryEnd" placeholder="入账月份止">
                                </div>

                            </div>

                            <div class="layui-col-md3" style="margin-top: 3px;margin-left: 19px;">
                                <label class="layui-form-label required" style="margin-top: -5px">发票类型:</label>
                                <div class="layui-input-inline" style="width: 50%">
                                    <!--                            <input type="text" name="invoiceType" class="layui-input" disabled/>-->
                                    <select id="invoiceTypeQuery" name="invoiceTypeQuery" lay-filter="invoiceTypeQuery">
                                        <option value=""></option>
                                        <option value="2">纸质普票</option>
                                        <option value="1">纸质专票</option>
                                        <option value="3">电子专票</option>
                                        <option value="4">增值税电子普通发票</option>
                                        <option value="5">预开票</option>
                                        <option value="6">数电专票（电子）</option>
                                        <option value="7">数电普票（电子）</option>
                                    </select>
                                </div>
                            </div>

                            <div class="layui-col-md3" style="margin-top: 3px;margin-left: -19px;">
                                <label class="layui-form-label" style="margin-top: -5px;width: 100px">业务金额</label>
                                <div class="layui-input-inline" style="width: 50%">
                                    <input class="layui-input" type="text" name="moneyQuery" id="moneyQuery" placeholder="业务金额"/>
                                </div>
                            </div>
                        </div>
                        <div class="layui-row">
                            <div class="layui-col-md3" style="margin-top: 3px">
                                <label class="layui-form-label" style="margin-top: -5px;width: 100px">分账商户</label>
                                <div class="layui-input-inline" style="width: 50%">
                                    <input class="layui-input" type="text" name="separateAccountMerchant" id="separateAccountMerchant" placeholder="请输入分账商户"/>
                                </div>
                            </div>
                            <div class="layui-col-md3" style="margin-top: 3px">
                                <label class="layui-form-label" style="margin-top: -5px;width: 100px">开通时间</label>
                                <div class="layui-input-inline" style="width: 87px">
                                    <input class="layui-input" name="openingTimeStart" id="openingTimeStart" placeholder="开通时间起">
                                </div>
                                -
                                <div class="layui-input-inline" style="width: 87px">
                                    <input class="layui-input" name="openingTimeEnd" id="openingTimeEnd" placeholder="开通时间止">
                                </div>
                            </div>
                            <div class="layui-col-md3" style="margin-top: 3px;margin-left: -51px">
                                <label class="layui-form-label" style="margin-top: -5px;width: 150px">绩效省份</label>
                                <div class="layui-input-inline" style="width:50%">
                                    <select class="layui-input" name="performanceProvince" lay-filter="performanceProvince" id="performanceProvince">
                                        <option value="">请选择</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="layui-row">
                        <div class="layui-col-md3" style="margin-top: 3px">
                            <label class="layui-form-label" style="margin-top: -5px;width: 100px">绩效地市</label>
                            <div class="layui-input-inline" style="width:50%">
                                <select class="layui-input" name="performanceCity" lay-filter="performanceCity" id="performanceCity">
                                    <option value="">请选择</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-col-md3" style="margin-top: 3px">
                        </div>
                        <div class="layui-col-md3" style="margin-top: 3px">
                        </div>
                        <div class="layui-col-md3" style="margin-top: -34px;" ID="searchDiv">
                            <a class="layui-btn" onclick="showMore()"><i class="layui-icon" id = 'icon'>&#xe61a;</i> 高级查询</a>
                            <button class="layui-btn" lay-submit="" lay-filter="search" id="search">
                                <i class="layui-icon">&#xe615;</i> 搜索
                            </button>
                            <button class="layui-btn" lay-submit="" lay-filter="reset" type="reset">
                                <i class="layui-icon">&#xe666;</i>重置
                            </button>
                        </div>
                    </div>
                </form>
                <div class="layui-row">
                    <div class="layui-col-md12">
                        <span>业务金额:</span>
                        <div class="layui-input-inline" style="color: red" id="totalMoney">
                            0
                        </div>
                        <span style="margin-left: 30px">可开票金额:</span>
                        <div class="layui-input-inline" style="color: red" id="noUsedMoney">
                            0
                        </div>
                        <span style="margin-left: 30px">已开票金额:</span>
                        <div class="layui-input-inline" style="color: red" id="usedMoney">
                            0
                        </div>
                        <span style="margin-left: 30px">已抵扣金额:</span>
                        <div class="layui-input-inline" style="color: red" id="dkMoneyTotal">
                            0
                        </div>

                        <span style="margin-left: 30px">需开票金额:</span>
                        <div class="layui-input-inline" style="color: red" id="needMoneyTotal">
                            0
                        </div>
                        <span style="margin-left: 30px">调整金额:</span>
                        <div class="layui-input-inline" style="color: red" id="adjustMentAmountTotal">
                            0
                        </div>

                        <span style="margin-left: 30px">已回款金额:</span>
                        <div class="layui-input-inline" style="color: red" id="backMoneyTotal">
                            0
                        </div>
                        <span style="margin-left: 30px">未回款金额:</span>
                        <div class="layui-input-inline" style="color: red" id="yetMoneyTotal">
                            0
                        </div>
                    </div>
                </div>
                <div class="layui-row">
                    <div class="layui-col-md12">
                        <table id="feeBusinessShipmentTable" lay-filter="toolTab"></table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div>
        <!--    此按钮隐藏，用于表格菜单上传按钮点击显示-->
        <a id="hideUpload" style="display: none">上传</a>
    </div>


    <div id="updateEntryMonthDiv" style="display: none">

        <form class="layui-form" id="updateEntryMonthForm" style="margin-left: 35px;">

            <div class="layui-row" style="margin-top: 15px">

                <div class="layui-form-item">
                    <label class="layui-form-label" style="margin-top: -5px;">归属月份:
                    </label>
                    <div class="layui-input-inline" style="width: 50%">
                        <input class="layui-input"   name="updateEntryMonth" id="updateEntryMonth" >
                    </div>
                </div>
            </div>

            <div class="layui-row">

                <div class="layui-col-md3"  style="margin-top: 3px;margin-left: -15px;">
                    <button type="button" class="layui-btn" lay-submit lay-filter="updateEntryMonthSubmit" id="updateEntryMonthSubmit" style="margin-left: 135px;">
                        <i class="layui-icon">&#xe615;</i>提交
                    </button>

                </div>
            </div>


        </form>

    </div>
    <!--账务拆分弹窗开始--->
    <div id="applyBreakdownShip" class="layui-fluid" style="display: none">
        <div class="layui-tab">
            <div class="layui-tab-content">
                <div class="layui-tab-item layui-show">
                    <div class="layui-fluid">
                        <form lay-filter="example" id="applyBreakdownShipForm" style="margin-left: 10px;">
                            <div class="layui-row">
                                <div class="layui-col-md6">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">NC客商名称：</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="ncName" readonly  required lay-verify="required" autocomplete="off" class="layui-input" style="border: none; background: transparent; outline: none;">
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-col-md6">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">NC编号：</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="ncNo" readonly required lay-verify="required"  autocomplete="off" class="layui-input" style="border: none; background: transparent; outline: none;">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-row">
                                <div class="layui-col-md6">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">业务月份：</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="businessMonth" readonly  required lay-verify="required" autocomplete="off" class="layui-input" style="border: none; background: transparent; outline: none;">
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-col-md6">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">产品类型：</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="productType" readonly required lay-verify="required"  autocomplete="off" class="layui-input" style="border: none; background: transparent; outline: none;">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-row">
                                <div class="layui-col-md6">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">对应业务大类：</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="bigType" readonly  required lay-verify="required" autocomplete="off" class="layui-input" style="border: none; background: transparent; outline: none;">
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-col-md6">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">业务类型：</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="newbusinessDesc" readonly required lay-verify="required"  autocomplete="off" class="layui-input" style="border: none; background: transparent; outline: none;">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-row">
                                <div class="layui-col-md6">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">业务金额：</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="money" readonly  required lay-verify="required" autocomplete="off" class="layui-input" style="border: none; background: transparent; outline: none;">
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-col-md6">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">调整金额：</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="adjustMentAmount" readonly required lay-verify="required"  autocomplete="off" class="layui-input" style="border: none; background: transparent; outline: none;">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-row">
                                <div class="layui-col-md6">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">抵扣款金额：</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="diMoney" readonly  required lay-verify="required" autocomplete="off" class="layui-input" style="border: none; background: transparent; outline: none;">
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-col-md6">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">已开票金额：</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="passInvoiceMoney" readonly required lay-verify="required"  autocomplete="off" class="layui-input" style="border: none; background: transparent; outline: none;">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-row">
                                <div class="layui-col-md6">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">已回款金额：</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="backedMoney" readonly  required lay-verify="required" autocomplete="off" class="layui-input" style="border: none; background: transparent; outline: none;">
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-col-md6">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">可拆帐金额：</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="breakDownMoney" readonly required lay-verify="required"  autocomplete="off" class="layui-input" style="border: none; background: transparent; outline: none;">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-row">
                                <div class="layui-col-md6">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">拆账明细：</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="breakDownMoney" readonly required lay-verify="required"  autocomplete="off" class="layui-input" style="border: none; background: transparent; outline: none;">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- 拆账明细表格 -->
                            <table id="accountTable" lay-filter="accountTable"></table>
                            <!-- 添加按钮 -->
<!--                            <button class="layui-btn" onclick="addBreakDownRow()">+添加</button>-->
                            <a class="layui-btn" lay-submit=""  onclick="addBreakDownRow()">
                                +添加
                            </a>
                            <div class="layui-col-md12" style="display: none;">
                                <div class="layui-col-md3" style="margin-top: 3px">
                                    <label class="layui-form-label" style="margin-top: -5px;width: 100px">审核结果:</label>
                                    <div class="layui-input-inline" style="width: 50%">
                                        <select class="layui-input" name="auditBreakDownStatus" id="auditBreakDownStatus">
                                            <option value="4">审核通过</option>
                                            <option value="2">审核拒绝</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">回退原因：</label>
                                    <div class="col-sm-10">
                                        <textarea class="form-control" rows="3" name="remark"></textarea>
                                    </div>
                                </div>
                            </div>
                        </form>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--账务拆分弹窗结束--->

    <!--修改数据-->
    <div id="updateFeeBussinessShip" class="layui-fluid" style="display: none">
        <div class="layui-tab">
            <div class="layui-tab-content">
                <div class="layui-tab-item layui-show">
                    <div class="layui-fluid">
                        <div class="layui-row">
                            <form action="" method="post"  lay-filter="example" id="updateFeeBussinessShipForm" style="margin-left: -49px;">

                                <div class="layui-row">
                                    <div class="layui-col-md12" style="margin-top: 10px">
                                        <label class="layui-form-label" style="margin-top: -5px;width: 150px;">业务主体:</label>
                                        <div class="layui-input-inline" style="width: 50%">
                                            <select class="layui-input"   class="businessEntity" name="updateBusinessEntity" lay-filter="updateBusinessEntity"
                                                    id="updateBusinessEntity">
                                                <option value="">请选择</option>
                                                <option value="福建国通星驿网络科技有限公司">福建国通星驿网络科技有限公司</option>
                                                <option value="福建云势数据科技服务有限公司">福建云势数据科技服务有限公司</option>
                                                <option value="上海杉昊智能科技发展有限公司">上海杉昊智能科技发展有限公司</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="layui-row">
                                    <div class="layui-col-md12" style="margin-top: 10px">
                                        <label class="layui-form-label" style="margin-top: -5px;width: 150px;">对应业务大类:</label>
                                        <div class="layui-input-inline" style="width: 50%">
                                            <select class="layui-input"  lay-filter="updateBigType" name="updateBigType" id="updateBigType">
                                                <option value="其他">其他</option>
                                                <option value="手续费返还">手续费返还</option>
                                                <option value="增值业务">增值业务</option>
                                                <option value="手续费贴补">手续费贴补</option>
                                                <option value="营销活动">营销活动</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>


                                <div class="layui-row">
                                    <div class="layui-col-md12" style="margin-top: 10px">
                                        <label class="layui-form-label" style="margin-top: -5px;width: 150px;">业务类型:</label>
                                        <div class="layui-input-inline" style="width: 50%">
                                            <select class="layui-input" name="updateBusinessDesc" id="updateBusinessDesc" lay-filter="updateBusinessDesc">

                                            </select>

                                        </div>
                                    </div>
                                </div>


                                <div class="layui-row">
                                    <div class="layui-col-md12" style="margin-top: 10px">
                                        <label class="layui-form-label" style="margin-top: -5px;width: 150px;">产品类型:</label>
                                        <div class="layui-input-inline" style="width: 50%">
                                            <select class="layui-input" name="updateProductType" id="updateProductType">
                                                <option value="其他">其他</option>
                                                <option value="邮驿付">邮驿付</option>
                                                <option value="星POS">星POS</option>
                                                <option value="慧徕店">慧徕店</option>
                                                <option value="星驿付">星驿付</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="layui-row">
                                    <div class="layui-col-md12" style="margin-top: 10px">
                                        <label class="layui-form-label" style="margin-top: -5px;width: 150px;">订单号:</label>
                                        <div class="layui-input-inline" style="width: 50%">
                                            <input type="text" class="layui-input"  id="updateOrderNumber" name="updateOrderNumber"  title="订单号">
                                        </div>
                                    </div>
                                </div>


                                <div class="layui-row">
                                    <div class="layui-col-md12" style="margin-top: 10px">
                                        <label class="layui-form-label" style="margin-top: -5px;width: 150px;">营销活动名称:</label>
                                        <div class="layui-input-inline" style="width: 50%">
                                            <input type="text" class="layui-input"  id="updateMarketActivityName" name="updateMarketActivityName"  title="营销活动名称">
                                        </div>
                                    </div>
                                </div>


                                <div class="layui-row">
                                    <div class="layui-col-md12" style="margin-top: 10px">
                                        <label class="layui-form-label" style="margin-top: -5px;width: 150px;">业务月份:</label>
                                        <div class="layui-input-inline" style="width: 50%">
                                            <input type="text" required   class="layui-input"  id="updateBusinessMonth" name="updateBusinessMonth"  title="业务月份">
                                        </div>
                                    </div>
                                </div>

                                <div class="layui-row">
                                    <div class="layui-col-md12" style="margin-top: 10px">
                                        <label class="layui-form-label" style="margin-top: -5px;width: 150px;">归属月份:</label>
                                        <div class="layui-input-inline" style="width: 50%">
                                            <input type="text" required   class="layui-input"  id="updateAttributiveMonth" name="updateAttributiveMonth"  title="归属月份">
                                        </div>
                                    </div>
                                </div>



                                <div class="layui-row">
                                    <div class="layui-col-md12" style="margin-top: 10px">
                                        <label class="layui-form-label" style="margin-top: -5px;width: 150px;">入账月份:</label>
                                        <div class="layui-input-inline" style="width: 50%">
                                            <input type="text" required   class="layui-input"  id="updateAccountMonth" name="updateAccountMonth"  title="入账月份">
                                        </div>
                                    </div>
                                </div>

                                <div class="layui-row">
                                    <div class="layui-col-md12" style="margin-top: 10px">
                                        <label class="layui-form-label" style="margin-top: -5px;width: 150px;">备注:</label>
                                        <div class="layui-input-inline" style="width: 50%">
                                            <input type="text" required   class="layui-input"  id="updateRemark" name="updateRemark"  title="备注">
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-row">
                                    <div class="layui-col-md12" style="margin-top: 10px">
                                        <label class="layui-form-label" style="margin-top: -5px;width: 150px;">客户类别:</label>
                                        <div class="layui-input-inline" style="width: 50%">
                                            <select class="layui-input" name="updateCustomerType" id="updateCustomerType" required>
                                                <option value="">请选择</option>
                                                <option value="商户">商户</option>
                                                <option value="代理">代理</option>
                                                <option value="邮政客户">邮政客户</option>
                                                <option value="银行客户">银行客户</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div id="separateAccountDiv" style="display: none">
                                    <div class="layui-row">
                                        <div class="layui-col-md12" style="margin-top: 10px">
                                            <label class="layui-form-label" style="margin-top: -5px;width: 150px;">分账商户:</label>
                                            <div class="layui-input-inline" style="width: 50%">
                                                <input type="text" required   class="layui-input"  id="updateSeparateAccountMerchant" name="updateSeparateAccountMerchant" title="分账商户">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-row">
                                        <div class="layui-col-md12" style="margin-top: 10px">
                                            <label class="layui-form-label" style="margin-top: -5px;width: 150px;">开通时间起:</label>
                                            <div class="layui-input-inline" style="width: 50%">
                                                <input type="text" required   class="layui-input"  id="updateOpeningTimeStart" name="updateOpeningTimeStart" title="开通时间起">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="layui-row">
                                        <div class="layui-col-md12" style="margin-top: 10px">
                                            <label class="layui-form-label" style="margin-top: -5px;width: 150px;">开通时间止:</label>
                                            <div class="layui-input-inline" style="width: 50%">
                                                <input type="text" required   class="layui-input"  id="updateOpeningTimeEnd" name="updateOpeningTimeEnd" title="开通时间止">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-row">
                                    <div class="layui-col-md12" style="margin-top: 10px">
                                        <label class="layui-form-label" style="margin-top: -5px;width: 150px;">绩效省份:</label>
                                        <div class="layui-input-inline" style="width: 50%">
                                            <select class="layui-input" name="updatePerformanceProvince" lay-filter="updatePerformanceProvince" id="updatePerformanceProvince">
                                                <option value="">请选择</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-row">
                                    <div class="layui-col-md12" style="margin-top: 10px">
                                        <label class="layui-form-label" style="margin-top: -5px;width: 150px;">绩效地市:</label>
                                        <div class="layui-input-inline" style="width: 50%">
                                            <select class="layui-input" name="updatePerformanceCity" lay-filter="updatePerformanceCity" id="updatePerformanceCity">
                                                <option value="">请选择</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-form-item" style="text-align: center;margin-top: 20px">

                                    <button type="button" class="layui-btn" lay-submit lay-filter="updateFeeBussinessShipSubmit" id="updateFeeBussinessShipSubmit" >
                                        <i class="layui-icon">&#xe615;</i>确定</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>


</body>

<!-- 表格操作列模板 -->
<script type="text/html" id="accountTableOperation">
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="deleteRow">删除</a>
</script>

<script>
    layui.config({
        base: "../../../js/layui_exts/"
    }).use(['table', 'laydate', 'form', 'excel', 'table', 'upload'], function () {
    var menuList = [];  //菜单权限
        $.ajax({
            async: false,
            url: '/web_posm/posmMenu/getAccountRoleByMenuId',
            data: {menuId: '1682'},
            success: function (data) {
                data = data.data;
                $.each(data, function (index, item) {
                    menuList.push(item.id);
                });
            }
        });
        var form = layui.form;
        var table = layui.table;
        var upload = layui.upload;
        var laydate = layui.laydate;
        queryMenuPermission("1682");

        var ncCustomerData = [];
    /**
     * 初始化拆账申请弹窗
     * @param data
     */
    function initApplyBreakdown(data) {
        layer.open({
            title: '拆账申请',
            content: $('#applyBreakdownShip'),
            type: 1,
            area: ['800px', '600px'],
            success: function(layero, index){
                // 填充表单数据
                $('#applyBreakdownShipForm [name="ncName"]').val(data.ncName);
                $('#applyBreakdownShipForm [name="ncNo"]').val(data.ncNo);
                $('#applyBreakdownShipForm [name="businessMonth"]').val(data.businessMonth);
                $('#applyBreakdownShipForm [name="productType"]').val(data.productType);
                $('#applyBreakdownShipForm [name="bigType"]').val(data.bigType);
                $('#applyBreakdownShipForm [name="newbusinessDesc"]').val(data.newbusinessDesc);
                $('#applyBreakdownShipForm [name="money"]').val(data.money);
                $('#applyBreakdownShipForm [name="adjustMentAmount"]').val(data.adjustMentAmount);
                $('#applyBreakdownShipForm [name="diMoney"]').val(data.diMoney);
                $('#applyBreakdownShipForm [name="passInvoiceMoney"]').val(data.passInvoiceMoney);
                $('#applyBreakdownShipForm [name="backedMoney"]').val(data.backedMoney);
                $('#applyBreakdownShipForm [name="breakDownMoney"]').val(data.breakDownMoney);

                // 初始化表格
                table.render({
                    elem: '#accountTable',
                    cols: [[
                        {field: 'seq', title: '序号', width: 80},
                        {field: 'ncName', title: 'NC客商名称', edit: 'text'},
                        {field: 'ncCode', title: 'NC编号', edit: 'text'},
                        {field: 'amount', title: '拆账金额', edit: 'text'},
                    ]],
                    data: [], // 初始为空
                    page: false
                });

                // 监听表格编辑事件
                table.on('edit(accountTable)', function(obj){
                    var value = obj.value;
                    var field = obj.field;
                    var data = table.cache['accountTable'];

                    loadNcCustomerData(value);

                    // 验证拆账金额
                    if(field === 'amount') {
                        if(isNaN(value) || parseFloat(value) <= 0) {
                            layer.msg('拆账金额必须为大于0的数字');
                            obj.update({
                                [field]: ''
                            });
                            return;
                        }

                        // 检查总拆账金额是否超过可拆账金额
                        var totalAmount = calculateTotalAmount(data);
                        var maxAmount = parseFloat(data.breakDownMoney);

                        if(totalAmount > maxAmount) {
                            layer.msg('总拆账金额不能超过可拆账金额: ' + maxAmount);
                            obj.update({
                                [field]: ''
                            });
                            return;
                        }
                    }

                    // 如果是NC名称编辑，自动填充NC编号
                    if(field === 'ncName') {
                        var ncCustomer = ncCustomerData.find(item => item.ncName === value);
                        if(ncCustomer) {
                            obj.update({
                                ncCode: ncCustomer.ncCode
                            });
                        }
                    }

                    // 监听行工具事件
                    table.on('tool(accountTable)', function(obj){
                        if(obj.event === 'deleteRow') {
                            layer.confirm('确定删除该行吗？', function(index){
                                var data = table.cache['accountTable'];
                                data.splice(obj.tr.index(), 1); // 删除对应行

                                // 重新排序
                                data.forEach(function(item, i) {
                                    item.seq = i + 1;
                                });

                                table.reload('accountTable', {
                                    data: data
                                });
                                layer.close(index);
                            });
                        }
                    });

                    // 更新全局变量
                    tableData = data;
                });
            }
        });
    }

    // 加载NC客户数据
    function loadNcCustomerData(data) {
        $.ajax({
            url: '/web_posm/feeBusinessShipment/getNcCustomerList',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                ncName: data
            }),
            success: function(res) {
                if(res.code === 200) {
                    ncCustomerData = res.data;
                } else {
                    layer.msg('获取NC客户数据失败: ' + res.msg, {icon: 2});
                }
            },
            error: function() {
                layer.msg('获取NC客户数据失败', {icon: 2});
            }
        });
    }

    // 计算总拆账金额
    function calculateTotalAmount(data) {
        var total = 0;
        data.forEach(function(item) {
            if(item.amount && !isNaN(item.amount)) {
                total += parseFloat(item.amount);
            }
        });
        return total;
    }

    // 提交拆账申请
    form.on('submit(applyBreakdownSubmit)', function(data) {
        var formData = data.field;
        var table = layui.table;
        var breakdownData = table.cache['accountTable'];

        // 验证拆账明细
        if(breakdownData.length === 0) {
            layer.msg('请至少添加一条拆账明细');
            return false;
        }

        // 验证总金额
        var totalAmount = calculateTotalAmount(breakdownData);
        var maxAmount = parseFloat(formData.breakDownMoney);

        if(totalAmount !== maxAmount) {
            layer.msg('总拆账金额必须等于可拆账金额: ' + maxAmount);
            return false;
        }

        // 准备提交数据
        var submitData = {
            originalId: formData.shipmentId, // 原业务ID
            ncName: formData.ncName,
            ncNo: formData.ncNo,
            businessMonth: formData.businessMonth,
            productType: formData.productType,
            bigType: formData.bigType,
            businessType: formData.newbusinessDesc,
            originalAmount: formData.money,
            adjustAmount: formData.adjustMentAmount,
            deductedAmount: formData.diMoney,
            invoicedAmount: formData.passInvoiceMoney,
            receivedAmount: formData.backedMoney,
            breakdownDetails: breakdownData.map(function(item) {
                return {
                    seq: item.seq,
                    ncName: item.ncName,
                    ncCode: item.ncCode,
                    amount: item.amount,
                    status: '0' // 初始状态为待审核
                };
            })
        };

        // 提交申请
        layer.confirm('确认提交拆账申请吗?', function(index) {
            $.ajax({
                url: '/web_posm/feeBusinessShipment/applyBreakdown',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(submitData),
                success: function(res) {
                    if(res.code === 200) {
                        layer.msg('拆账申请提交成功', {icon: 1});
                        layer.closeAll();
                        reloadTable(); // 刷新表格
                    } else {
                        layer.msg(res.msg || '拆账申请提交失败', {icon: 2});
                    }
                },
                error: function() {
                    layer.msg('服务器错误，请稍后再试', {icon: 2});
                }
            });
        });

        return false;
    });

    // 拆账审核相关逻辑
    // 初始化拆账审核弹窗
    function initAuditBreakdown(breakdownId) {
        // 加载拆账申请详情
        $.ajax({
            url: '/web_posm/feeBusinessShipment/detailBreakDownFee',
            type: 'POST',
            data: {breakdownId: breakdownId},
            success: function(res) {
                // 填充表单数据
                $('#applyBreakdownShipForm [name="ncName"]').val(data.ncName);
                $('#applyBreakdownShipForm [name="ncNo"]').val(data.ncNo);
                $('#applyBreakdownShipForm [name="businessMonth"]').val(data.businessMonth);
                $('#applyBreakdownShipForm [name="productType"]').val(data.productType);
                $('#applyBreakdownShipForm [name="bigType"]').val(data.bigType);
                $('#applyBreakdownShipForm [name="newbusinessDesc"]').val(data.newbusinessDesc);
                $('#applyBreakdownShipForm [name="money"]').val(data.money);
                $('#applyBreakdownShipForm [name="adjustMentAmount"]').val(data.adjustMentAmount);
                $('#applyBreakdownShipForm [name="diMoney"]').val(data.diMoney);
                $('#applyBreakdownShipForm [name="passInvoiceMoney"]').val(data.passInvoiceMoney);
                $('#applyBreakdownShipForm [name="backedMoney"]').val(data.backedMoney);
                $('#applyBreakdownShipForm [name="breakDownMoney"]').val(data.breakDownMoney);
                if(res.code === 200) {
                    var data = res.data;

                    layer.open({
                        title: '拆账审核 - ' + data.breakdownNo,
                        content: $('#auditBreakdownDiv'),
                        type: 1,
                        area: ['900px', '600px'],
                        success: function() {
                            // 渲染拆账明细表格
                            table.render({
                                elem: '#auditBreakdownTable',
                                cols: [[
                                    {field: 'seq', title: '序号', width: 80},
                                    {field: 'ncName', title: 'NC客商名称'},
                                    {field: 'ncCode', title: 'NC编号'},
                                    {field: 'amount', title: '拆账金额'},
                                ]],
                                data: data.breakdownDetails,
                                page: false
                            });
                        }
                    });
                } else {
                    layer.msg(res.msg || '获取拆账详情失败', {icon: 2});
                }
            }
        });
    }

    // 提交审核
    form.on('submit(submitAudit)', function(data) {
        var formData = data.field;
        var breakdownId = currentBreakdownId; // 当前审核的拆账ID

        if(!formData.auditResult) {
            layer.msg('请选择审核结果', {icon: 2});
            return false;
        }

        layer.confirm('确认提交审核结果吗?', function(index) {
            $.ajax({
                url: '/web_posm/feeBusinessShipment/auditBreakDownFee',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    breakdownId: breakdownId,
                    auditResult: formData.auditResult,
                    auditComment: formData.auditComment
                }),
                success: function(res) {
                    if(res.code === 200) {
                        layer.msg('审核提交成功', {icon: 1});
                        layer.closeAll();
                        reloadTable(); // 刷新表格
                    } else {
                        layer.msg(res.msg || '审核提交失败', {icon: 2});
                    }
                }
            });
        });

        return false;
    });
    // 重新拆账相关逻辑

    // 初始化重新拆账弹窗
    function initReBreakdown(breakdownId) {
        // 加载原拆账信息
        $.ajax({
            url: '/web_posm/feeBusinessShipment/detailBreakDownFee',
            type: 'GET',
            data: {breakdownId: breakdownId},
            success: function(res) {
                // 填充表单数据
                $('#applyBreakdownShipForm [name="ncName"]').val(data.ncName);
                $('#applyBreakdownShipForm [name="ncNo"]').val(data.ncNo);
                $('#applyBreakdownShipForm [name="businessMonth"]').val(data.businessMonth);
                $('#applyBreakdownShipForm [name="productType"]').val(data.productType);
                $('#applyBreakdownShipForm [name="bigType"]').val(data.bigType);
                $('#applyBreakdownShipForm [name="newbusinessDesc"]').val(data.newbusinessDesc);
                $('#applyBreakdownShipForm [name="money"]').val(data.money);
                $('#applyBreakdownShipForm [name="adjustMentAmount"]').val(data.adjustMentAmount);
                $('#applyBreakdownShipForm [name="diMoney"]').val(data.diMoney);
                $('#applyBreakdownShipForm [name="passInvoiceMoney"]').val(data.passInvoiceMoney);
                $('#applyBreakdownShipForm [name="backedMoney"]').val(data.backedMoney);
                $('#applyBreakdownShipForm [name="breakDownMoney"]').val(data.breakDownMoney);
                if(res.code === 200) {
                    var data = res.data;
                    currentReBreakdownId = breakdownId;

                    layer.open({
                        title: '重新拆账 - ' + data.breakdownNo,
                        content: $('#reBreakdownDiv'),
                        type: 1,
                        area: ['900px', '600px'],
                        success: function() {
                            // 填充原拆账信息
                            $('#reBreakdownForm [name="originalNo"]').val(data.breakdownNo);

                            // 渲染拆账明细表格
                            table.render({
                                elem: '#reBreakdownTable',
                                cols: [[
                                    {type: 'checkbox', fixed: 'left'},
                                    {field: 'seq', title: '序号', width: 80},
                                    {field: 'ncName', title: 'NC客商名称', edit: 'text'},
                                    {field: 'ncCode', title: 'NC编号', edit: 'text'},
                                    {field: 'amount', title: '拆账金额', edit: 'text'}
                                ]],
                                data: data.breakdownDetails,
                                page: false
                            });
                        }
                    });
                } else {
                    layer.msg(res.msg || '获取拆账详情失败', {icon: 2});
                }
            }
        });
    }

    // 添加重新拆账行
    function addReBreakdownRow() {
        var table = layui.table;
        var data = table.cache['reBreakdownTable'] || [];
        var seq = data.length + 1;

        table.reload('reBreakdownTable', {
            data: data.concat([{
                seq: seq,
                ncName: '',
                ncCode: '',
                amount: ''
            }])
        });
    }

    // 删除重新拆账行
    function deleteReBreakdownRow() {
        var table = layui.table;
        var checkStatus = table.checkStatus('reBreakdownTable');

        if(checkStatus.data.length === 0) {
            layer.msg('请至少选择一行删除', {icon: 2});
            return;
        }

        var data = table.cache['reBreakdownTable'];
        var deleteIds = checkStatus.data.map(function(item) { return item.LAY_TABLE_INDEX; });

        // 过滤掉选中的行
        var newData = data.filter(function(item, index) {
            return deleteIds.indexOf(index) === -1;
        });

        // 重新排序
        newData.forEach(function(item, index) {
            item.seq = index + 1;
        });

        table.reload('reBreakdownTable', {
            data: newData
        });
    }

    // 提交重新拆账
    form.on('submit(submitReBreakdown)', function(data) {
        var table = layui.table;
        var breakdownData = table.cache['reBreakdownTable'];

        // 验证拆账明细
        if(breakdownData.length === 0) {
            layer.msg('请至少添加一条拆账明细');
            return false;
        }

        // 验证总金额
        var totalAmount = calculateTotalAmount(breakdownData);
        var originalData = table.cache['feeBusinessShipmentTable'].find(function(item) {
            return item.shipmentId === currentReBreakdownId;
        });

        if(!originalData) {
            layer.msg('获取原数据失败，请刷新后重试', {icon: 2});
            return false;
        }

        var maxAmount = parseFloat(originalData.breakDownMoney);

        if(totalAmount > maxAmount) {
            layer.msg('总拆账金额不能超过可拆账金额: ' + maxAmount);
            return false;
        }

        // 准备提交数据
        var submitData = {
            originalId: currentReBreakdownId,
            breakdownDetails: breakdownData.map(function(item) {
                return {
                    seq: item.seq,
                    ncName: item.ncName,
                    ncCode: item.ncCode,
                    amount: item.amount
                };
            })
        };

        // 提交申请
        layer.confirm('确认提交重新拆账吗?', function(index) {
            $.ajax({
                url: '/web_posm/feeBusinessShipment/editBreakDownFee',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(submitData),
                success: function(res) {
                    if(res.code === 200) {
                        layer.msg('重新拆账提交成功', {icon: 1});
                        layer.closeAll();
                        reloadTable(); // 刷新表格
                    } else {
                        layer.msg(res.msg || '重新拆账提交失败', {icon: 2});
                    }
                },
                error: function() {
                    layer.msg('服务器错误，请稍后再试', {icon: 2});
                }
            });
        });

        return false;
    });


    /**
     * 批量申请
     */
    function batchInvoice() {
        var checkData = layui.table.checkStatus('feeBusinessShipmentTableId'); //idTest 即为基础参数 id 对应的值
        var length = checkData.data.length;

        if(length === 0){
            layer.alert("请先勾选手续费明细进行开票", {icon: 6}, function () {
                layer.closeAll('dialog');
            });

            return;
        }

        // if(length > 7){
        //     layer.alert("一张票的开票内容不能超过7行", {icon: 6}, function () {
        //         layer.closeAll('dialog');
        //     });
        //
        //     return;
        // }

        var shipmentIdList = [];
        var firstData = checkData.data[0];  //第一条数据
        var rowData = null;
        var busEntity = firstData.busEntity;

        for(var i in checkData.data){
            rowData = checkData.data[i];

            // if(rowData.businessTypeDesc !== firstData.businessTypeDesc){
            //     layer.alert("存在母单业务业务类型不一致，不允许开票", {icon: 6},
            //         function () {
            //             layer.closeAll('dialog');
            //         });
            //
            //     return;
            // }

            if(rowData.remainingMoney === 0){
                layer.alert("该手续费业务明细无可开票金额", {icon: 6},
                    function () {
                        layer.closeAll('dialog');
                    });

                return;
            }

            if(rowData.ncNo !== firstData.ncNo){
                layer.alert("NC编号不同，无法合并开票", {icon: 6},
                    function () {
                        layer.closeAll('dialog');
                    });

                return;
            }
            if(rowData.busEntity !== '福建国通星驿网络科技有限公司' && rowData.busEntity !== '上海杉昊智能科技发展有限公司'){
                layer.alert("暂不支持开票", {icon: 6},
                    function () {
                        layer.closeAll('dialog');
                    });

                return;
            }
            if(rowData.busEntity !== busEntity){
                layer.alert("业务主体不同，不允许开票", {icon: 6},
                    function () {
                        layer.closeAll('dialog');
                    });

                return;
            }
            shipmentIdList.push(rowData.shipmentId);
        }

        window.shipmentIdList = shipmentIdList;
        window.shipment = firstData;
        window.businessEntity = busEntity;
        window.businessEntityCode = firstData.businessEntityCode;

        layer.open({
            area: ['1600px', '650px'],
            type: 2,
            title: '',
            content: 'fee-business-invoice-apply.html'
        });
    }
    function updateConfirmStatus(confirmStatus) {
        var checkStatus = layui.table.checkStatus('feeBusinessShipmentTableId'); //idTest 即为基础参数 id 对应的值
        var shipmentIdArray = [];
        var msg = "";

        if (confirmStatus === 0) {
            msg = "取消";
        }

        if (checkStatus.data.length === 0) {
            layer.alert("请先勾选要" + msg + "确认的数据", {icon: 6}, function () {
                layer.closeAll('dialog');
            });

            return false;
        }

        for (var i in checkStatus.data) {
            shipmentIdArray.push(checkStatus.data[i].shipmentId);
        }

        layer.confirm("是否批量" + msg + "确认数据?", function (index) {
            $.ajax({
                url: '/web_posm/shipment/updateConfirmStatus',
                data: JSON.stringify({"shipmentIdArray": shipmentIdArray, "confirmStatus" : confirmStatus,"flag":"1"}),
                type: "POST",
                contentType: "application/json;charset=utf-8",
                dataType: "json",
                success: function (res) {
                    layer.alert("操作成功！", {icon: 6}, function () {
                        layer.closeAll('dialog');
                        $("#search").click();
                    });
                }
            });
        });
    }


        //业务月份起
        laydate.render({
            elem: '#businessMonthStart',
            format: 'yyyyMM',
            type: 'month',
            done: function (value, date, endDate) {
                var startDate = value;
                var endTime = $('#businessMonthEnd').val('');
                if (startDate !== "" && startDate != null && endTime !== "" && endTime != null) {
                    if (endTime < startDate) {
                        layer.msg('结束时间不能小于开始时间');
                        $('#businessMonthStart').val('');
                    }
                }
            }
        })

        //业务月份止
        laydate.render({
            elem: '#businessMonthEnd',
            format: 'yyyyMM',
            type: 'month',
            done: function (value, date, endDate) {
                var startDate = $('#businessMonthStart').val();
                var endTime = value;
                if (startDate !== "" && startDate != null && endTime !== "" && endTime != null) {
                    if (endTime < startDate) {
                        layer.msg('结束时间不能小于开始时间');
                        $('#businessMonthEnd').val('');
                    }
                }
            }
        })

        laydate.render({
            elem: '#updateEntryMonth',
            format: 'yyyyMM',
            type: 'month'
        })

        //归属月份起
        laydate.render({
            elem: '#entryMonthQueryStart',
            format: 'yyyyMM',
            type: 'month',
            done: function (value, date, endDate) {
                var startDate = value;
                var endTime = $('#entryMonthQueryEnd').val();
                if (startDate !== "" && startDate != null && endTime !== "" && endTime != null) {
                    if (endTime < startDate) {
                        layer.msg('结束时间不能小于开始时间');
                        $('#entryMonthQueryStart').val('');
                    }
                }
            }
        })
        //归属月份止
        laydate.render({
            elem: '#entryMonthQueryEnd',
            format: 'yyyyMM',
            type: 'month',
            done: function (value, date, endDate) {
                var startDate = $('#entryMonthQueryStart').val();
                var endTime = value;
                if (startDate !== "" && startDate != null && endTime !== "" && endTime != null) {
                    if (endTime < startDate) {
                        layer.msg('结束时间不能小于开始时间');
                        $('#entryMonthQueryEnd').val('');
                    }
                }
            }
        })
        //入账月份起
        laydate.render({
            elem: '#addAccountMonthQueryStart',
            format: 'yyyyMM',
            type: 'month',
            done: function (value, date, endDate) {
                var startDate = value;
                var endTime = $('#addAccountMonthQueryEnd').val();
                if (startDate !== "" && startDate != null && endTime !== "" && endTime != null) {
                    if (endTime < startDate) {
                        layer.msg('结束时间不能小于开始时间');
                        $('#addAccountMonthQueryStart').val('');
                    }
                }
            }
        })
        //入账月份止
        laydate.render({
            elem: '#addAccountMonthQueryEnd',
            format: 'yyyyMM',
            type: 'month',
            done: function (value, date, endDate) {
                var startDate = $('#addAccountMonthQueryStart').val('');
                var endTime = value;
                if (startDate !== "" && startDate != null && endTime !== "" && endTime != null) {
                    if (endTime < startDate) {
                        layer.msg('结束时间不能小于开始时间');
                        $('#addAccountMonthQueryEnd').val('');
                    }
                }
            }
        });

        //开通时间起
        laydate.render({
            elem: '#openingTimeStart',
            format: 'yyyyMMdd'
        });

        //开通时间止
        laydate.render({
            elem: '#openingTimeEnd',
            format: 'yyyyMMdd'
        });

         //省份
        geographySelectQuery('feeBussInProvinceId', '/web_posm/geography/queryProvince', null, '');

        //根据省份下拉框动态加载城市数据
        form.on('select(feeBussInProvinceId)', function (data) {
            geographySelectQuery('feeBussInCityId', '/web_posm/geography/queryCityByProvId', {"provinceId" : data.value}, "");
        });

        //绩效省份
        geographySelectQuery('performanceProvince', '/web_posm/geography/queryProvince', null, '');

        //绩效地市
        form.on('select(performanceProvince)', function (data) {
            geographySelectQuery('performanceCity', '/web_posm/geography/queryCityByProvId', {"provinceId" : data.value}, "");
        });

        //编辑功能的绩效省份下拉框监听
        $("#updatePerformanceProvince").change(function() {
            geographySelectQuery('updatePerformanceCity', '/web_posm/geography/queryCityByProvId', {"provinceId" : $("#updatePerformanceProvince").val()}, "");
        });

        $("#updateBigType").change(function() {
            $("#separateAccountDiv").hide();
            var typeList=[];
            $.ajax({
                url: '/web_posm/financialBusinessType/typeList',
                type: "POST",
                contentType: "application/json;charset=utf-8",
                data: $("#updateBigType").val(),
                dataType: "json",
                success: function (res) {
                    var text=" <option value=\"\">请选择</option>-->"
                    for(var i = 0, len = res.data.length; i < len; i++) {
                        if(typeList.indexOf(res.data[i].value )!==-1){
                            continue;
                        }
                        text += "<option value='" + res.data[i].value + "'>" + res.data[i].value + "</option>";
                        typeList.push(res.data[i].value)
                    }
                    $('#updateBusinessDesc').html(text);
                    $("#updateBusinessDesc").val('');
                    layui.form.render();
                },
                error() {
                }
            });
        });

        $("#updateBusinessDesc").change(function() {
            if ($("#updateBusinessDesc").val() === '分账') {
                $("#separateAccountDiv").show();
                var province = $("#updatePerformanceProvince").val();

                if (isEmpty(province)) {
                    geographySelectQuery('updatePerformanceProvince', '/web_posm/geography/queryProvince', null, null);
                } else {
                    geographySelectQuery('updatePerformanceProvince', '/web_posm/geography/queryProvince', null, province);

                    var city = $("#updatePerformanceCity").val();

                    if (isEmpty(city)) {
                        geographySelectQuery('updatePerformanceCity', '/web_posm/geography/queryCityByProvId', {"provinceId" : province}, null);
                    } else {
                        geographySelectQuery('updatePerformanceCity', '/web_posm/geography/queryCityByProvId', {"provinceId" : province}, city);
                    }
                }
            } else {
                $("#separateAccountDiv").hide();
            }
        });

        form.on('select(updateBigType)', function (data) {
            console.log("Select发生了变动！");
        })
        form.on('select(bigType)', function (data) {
            // var c = {
            //     bigType:
            // };
            var typeList=[];
            $.ajax({
                url: '/web_posm/financialBusinessType/typeList',
                type: "POST",
                contentType: "application/json;charset=utf-8",
                data: data.value,
                dataType: "json",
                success: function (res) {
                    var text=" <option value=\"\">请选择</option>-->"
                    for(var i = 0, len = res.data.length; i < len; i++) {
                        if(typeList.indexOf(res.data[i].value )!==-1){
                            continue;
                        }
                        text += "<option value='" + res.data[i].value + "'>" + res.data[i].value + "</option>";
                        typeList.push(res.data[i].value)
                    }
                    $('#newbusinessDesc').html(text);
                    layui.form.render();
                },
                error() {
            }
            });

        });

        //发货日期开始时间
        laydate.render({
            elem: '#huiKuanTimeStart',
            format: 'yyyyMMdd',
            done: function (value, date, endDate) {
                var startDate = value;
                var endTime = $('#huiKuanTimeEnd').val();
                if (startDate !== "" && startDate != null && endTime !== "" && endTime != null) {
                    if (endTime < startDate) {
                        layer.msg('结束时间不能小于开始时间');
                        $('#huiKuanTimeStart').val('');
                    }
                }
            }
        });

        //发货日期结束时间
        laydate.render({
            elem: '#huiKuanTimeEnd',
            format: 'yyyyMMdd',
            done: function (value, date, endDate) {
                var startDate = $('#huiKuanTimeStart').val();;
                var endTime = value;
                if (startDate !== "" && startDate != null && endTime !== "" && endTime != null) {
                    if (endTime < startDate) {
                        layer.msg('结束时间不能小于开始时间');
                        $('#huiKuanTimeEnd').val('');
                    }
                }
            }
        });


        configFormatTime("createTimeStart", "yyyyMMdd");
        configFormatTime("createTimeEnd", "yyyyMMdd");
        configFormatTime("openingTimeStart", "yyyyMMdd");
        configFormatTime("openingTimeEnd", "yyyyMMdd");

        configFormatTime("updateOpeningTimeStart", "yyyyMMdd");
        configFormatTime("updateOpeningTimeEnd", "yyyyMMdd");

        //queryFinancialBusinessType("2");
        // queryFinancialNewBusinessType();
        queryFinancialProductType();
        queryFinancialBigType('bigType');
        var tableBase = table.render({
            elem: '#feeBusinessShipmentTable',
            // url: "/web_posm/feeBusinessShipment/queryFeeBusinessShipmentDetail",  //数据接口
            data: [],
            page: {
                layout: ['limit', 'count', 'prev', 'page', 'next', 'skip']
            }, //开启分页
            limit: 200,
            limits:[10,20,30,40,50,100,200,500],
            height: 'full-300',
            method: 'post',
            contentType: 'application/json',
            toolbar: '#toolbarDemo',  //开启头部工具栏，并为其绑定左侧模板
            defaultToolbar: ['filter'],
            cols: [[ //表头
                {type:'checkbox',width:50},
                {title:'操作', width:"200", toolbar:'#operationTab'},
                {field:'shipmentId', title:'业务流水号', width:"150", align:'center'},
                {field:'invoiceId', title:'申请编号', width:"150", align:'center', hide: true},
                {field:'ncNo', title:'NC编号', width:"100", align:'center', hide: true},
                {field:'busEntity', title:'业务主体', width:"320", align:'center'},
                {field:'ncName', title:'NC名称', width:"320", align:'center'},
                {field:'businessMonth', title:'业务月份', width:"80", align:'center'},
                {field:'newbusinessDesc', title:'业务类型', width:"130", align:'center'},
                {field:'productType', title:'产品类型', width:"130", align:'center'},
                {field:'bigType', title:'对应业务大类', width:"130", align:'center'},
                {field:'invoiceNo', title:'发票号', width:"90", align:'center'},
                {field: 'confirmStatus', title: '确认状态', width: "90", align:'center'},

                {field: 'breakDownStatus', title: '拆账状态', width: "90", align:'center'},
                {field: 'entryMonth', title: '归属月份', width: "90", align:'center', hide: true},
                {field: 'serviceRate', title: '服务费率', width: "90", align:'center'},
                {field:'money', title:'业务金额', width:"90", align:'center'},
                {field: 'adjustMentAmount', title: '调整金额', width: "90", align:'center'},
                {field: 'breakDownMoney', title: '拆账金额', width: "90", align:'center'},
                {field:'needOpenMoney', title:'需开票金额', width:"90", align:'center'},

                {field:'passInvoiceMoney', title:'已开票金额', width:"90", align:'center', hide: true},
                {field:'remainingMoney', title:'可开票余额', width:"90", align:'center'},
                {field: 'backedMoney', title: '已回款金额', width: "90",
                    templet: function (d) {
                        if ('.' == d.backedMoney.substring(0, 1)) {
                            return '0' + d.backedMoney;
                        } else {
                            return d.backedMoney;
                        }
                    }},
                {field: 'diMoney', title: '抵扣款金额', width: "90",
                    templet: function (d) {
                        if ('.' == d.diMoney.substring(0, 1)) {
                            return '0' + d.diMoney;
                        } else {
                            return d.diMoney;
                        }
                    }, hide: true
                },
                {field: 'yetMoney', title: '未回款金额', width: "90",
                    templet: function (d) {
                        if ('.' == d.yetMoney.substring(0, 1)) {
                            return '0' + d.yetMoney;
                        } else {
                            return d.yetMoney;
                        }
                    }},
                {field: 'backDate', title: '回款日期', width: "90", hide: true},
                {field: 'backStatus', title: '回款状态', width: "90"},

                {field: 'province', title: '省份', width: "90"},
                {field: 'city', title: '地市', width: "90"},
                {field: 'orderNumber', title: '订单号', width: "90"},
                {field: 'marketActivityName', title: '营销活动名称', width: "90"},
                {field: 'customerType', title: '客户类别', width: "90"},

                {field: 'bussinessOne', title: '入账月份', width: "90"},
                {field: 'bussinessTwo', title: '业务2', width: "90"},
                {field: 'bussinessThree', title: '业务3', width: "90"},
                {field: 'bussinessFour', title: '业务4', width: "90"},
                {field: 'bussinessFive', title: '业务5', width: "90"},


                {field:'applyStatusDesc', title:'申请状态', width:"120", align:'center', hide: true},
                {field:'createStaff', title:'导入人', width:"90", align:'center', hide: true},
                {field:'auditStatusDesc', title:'审核状态', width:"110", align:'center', hide: true},
                {field:'applyStaff', title:'申请人', width:"90", align:'center', hide: true},
                {field:'applyStatus', title:'申请状态', hide: true},
                {field:'applyTime', title:'申请时间', width:"130", align:'center', hide: true},
                {field:'auditStaff', title:'审核人', width:"90", align:'center', hide: true},
                {field:'auditStatus', title:'审核状态', hide: true},
                {field:'auditTime', title:'审核时间', width:"130", align:'center', hide: true},
                {field:'remarks', title:'备注', width:"90", align:'center'},
                {field: 'invoiceRemark', title: '票面备注', width: "100", hide: true},
                {field: 'signStatus', title: '签收状态', width: "70"},
                {field: 'signId', title: '签收人', width: "70", hide: true},
                {field: 'signTime', title: '签收时间', width: "70", hide: true},
                {field: 'shipmentNum', title: '物流单号', width: "90"},
                {field: 'invoiceType', title: '发票类型', width: "90"},

                {field: 'separateAccountMerchant', title: '分账商户', width: "90"},
                {field: 'openingTime', title: '开通时间段', width: "90"},
                {field: 'performanceProvince', title: '绩效省份', width: "90"},
                {field: 'performanceCity', title: '绩效地市', width: "90"},
            ]],
            id: 'feeBusinessShipmentTableId',
            parseData: function (res) { //将原始数据解析成 table 组件所规定的数据
                return {
                    "code": res.code, //解析接口状态
                    "msg": res.msg, //解析提示文本
                    "count": res.count, //解析数据长度
                    "data": res.data//解析数据列表
                };
            },
            done: function (res, curr, count) {
                if (menuList.indexOf(1696) === -1) {
                    $("#importShipment").hide();
                }
                if (menuList.indexOf(2615) === -1) {
                    $("#confirmStatusBtn").hide();
                }
                if (menuList.indexOf(2615) === -1) {
                    $("#cancelConfirmStatusBtn").hide();
                }

                if (menuList.indexOf(1697) === -1) { //导出
                    $("#exportShipment").hide();
                }

                if (menuList.indexOf(1698) === -1) {  //下载出货明细模板
                    $("#exportModel").hide();
                }

                if (menuList.indexOf(1699) === -1) {
                    $("#batchInvoice").hide();
                }

                if (menuList.indexOf(2670) === -1) {
                    $("#entryMonth").hide();
                }

                if (menuList.indexOf(1700) === -1) {
                    $("#delete").hide();
                }
                if (menuList.indexOf(2106) === -1) {
                   $(".layui-btn").each(function () {
                        if ($(this).attr('lay-event') == 'confirmReceipt') {
                                $(this).hide();
                        }
                    });
                }
                if (menuList.indexOf(2107) === -1) {
                   $(".layui-btn").each(function () {
                        if ($(this).attr('lay-event') == 'cConfirmReceipt') {
                                $(this).hide();
                        }
                    });
                }

                if (menuList.indexOf(2753) === -1) {
                    $(".layui-btn").each(function () {
                        if ($(this).attr('lay-event') == 'edit') {
                            $(this).hide();
                        }
                    });
                }
            }
        });

        //监听表格按钮事件
        table.on('toolbar(toolTab)', function (obj) {


            var checkStatus = table.checkStatus(obj.config.id);
            var data= checkStatus.data;

            switch (obj.event) {
                case 'confirmStatus':
                    updateConfirmStatus(1);
                    break;
                case "cancelConfirmStatus":
                    updateConfirmStatus(0);
                    break;
                case 'importShipment':
                    $("#hideUpload").click();
                    break;
                case 'exportShipment':
                    exportExcelFile('/web_posm/feeBusinessShipment/exportFeeBusinessShipment' , obj.config.where);;
                    break;
                case 'exportModel':
                    exportFeeBussinessModelFile(1);
                    break;
                case 'batchInvoice':
                    batchInvoice();
                    break;
                case 'entryMonth':
                    layer.open({
                        moveOut: true,//是否可移动到窗口外
                        type: 1,//可传入的值有：0（信息框，默认）1（页面层）2（iframe层）3（加载层）4（tips层）
                        title: '编辑归属月份',
                        content: $('#updateEntryMonthDiv'),
                        area: ['400px', '200px'],
                        btnAlign: 'c',
                        success: function (layero, fatherIndex) {

                            //监编辑按钮
                            form.on('submit(updateEntryMonthSubmit)', function () {

                                layer.confirm('是否确认维护', {icon: 6, title: '确认'}, function (index) {
                                    var shipmentIds = '';
                                    for (let j = 0; j < data.length; j++) {
                                        shipmentIds += data[j].shipmentId + ";";
                                    }

                                      var updateEntryMonth = $("#updateEntryMonth").val();


                                    $.ajax({
                                        url: "/web_posm/feeBusinessShipment/updateEntryMonth",
                                        type: 'post',
                                        data: {
                                            "shipmentIds": shipmentIds,
                                            "updateEntryMonth": updateEntryMonth
                                        },
                                        success: function (res) {
                                            layer.close(index);
                                            layer.close(fatherIndex);
                                            reloadTableFeeBusinessShipment();

                                        }
                                    });

                                });
                            })

                        },
                        end: function () {

                            $("#updateEntryMonth").val('');

                            form.render();

                        }

                    });
                    break;
                case 'delete':
                    var selectRows = table.checkStatus('feeBusinessShipmentTableId');
                    newDeleteShipment(selectRows,2);
                    break;
                case 'generateStampDataBtn':
                    var selectRows = table.checkStatus('feeBusinessShipmentTableId');
                    if (selectRows.data.length == 0) {
                        layer.close(layer.index);
                        layer.msg("请至少选择一项", {icon: '5'});
                        return;
                    }

                    // 获取当前页面的 URL
                    var queryString = window.location.search;
                    // 使用 URLSearchParams 解析查询字符串
                    var urlParams = new URLSearchParams(queryString);
                    // 获取具体的参数值
                    var stamp = urlParams.get('stamp');
                    var bigType = urlParams.get('bigType');//对应业务大类

                    if('手续费贴补'===bigType){
                        // window.parent.$('#iframeModal1').fadeOut();//调用父层代码隐藏此页面
                        window.parent.generateStampDataBtn(bigType,selectRows);//调用父页面方法,将选择的数据传给父页面
                    } else if('营销活动'===bigType){
                        // window.parent.$('#iframeModal2').fadeOut();//调用父层代码隐藏此页面
                        window.parent.generateStampDataBtn(bigType,selectRows);//调用父页面方法,将选择的数据传给父页面
                    }
                    break;

                // case 'updateStampDataBtn':
                //     var selectRows = table.checkStatus('feeBusinessShipmentTableId');
                //     if (selectRows.data.length == 0) {
                //         layer.close(layer.index);
                //         layer.msg("请至少选择一项", {icon: '5'});
                //         return;
                //     }
                //
                //     // 获取当前页面的 URL
                //     var queryString = window.location.search;
                //     // 使用 URLSearchParams 解析查询字符串
                //     var urlParams = new URLSearchParams(queryString);
                //     // 获取具体的参数值
                //     var stamp = urlParams.get('stamp');
                //     var bigType = urlParams.get('bigType');//对应业务大类
                //
                //     if('手续费贴补'===bigType){
                //         // window.parent.$('#iframeModal1').fadeOut();//调用父层代码隐藏此页面
                //         window.parent.updateStampDataBtn(bigType,selectRows);//调用父页面方法,将选择的数据传给父页面
                //     } else if('营销活动'===bigType){
                //         // window.parent.$('#iframeModal2').fadeOut();//调用父层代码隐藏此页面
                //         window.parent.updateStampDataBtn(bigType,selectRows);//调用父页面方法,将选择的数据传给父页面
                //     }
                //     break;
            }
        });

        uploadExcelFile(2);

        table.on('tool(toolTab)', function (obj) {
            var data = obj.data; //获得当前行数据
            var event = obj.event; //获得 lay-event 对应的值（也可以是表头的 event 参数对应的值）
            var tr = obj.tr; //获得当前行 tr 的 DOM 对象（如果有的话）

            if(event=='deleteShipment') {
                    deleteShipment(data, 2);
            }else if(event=='cConfirmReceipt'){
                var invoiceNo="";
                if(data.invoiceNo!=undefined){
                    invoiceNo=data.invoiceNo;
                };
                var shipmentId=data.shipmentId;
                    $.ajax({
                        url: '/web_posm/feeBusinessShipment/cancelConfirmReceipt?invoiceNo='+invoiceNo+'&shipmentId='+shipmentId,
                        //type: "POST",
                        //contentType: "application/json;charset=utf-8",
                       // data: {"invoiceNo":data.invoiceNo,"shipmentId":data.shipmentId},
                        //dataType: "json",
                        success: function (res) {
                            layer.closeAll();
                            layer.alert(res.msg, {icon: 6}, function () {
                                layer.closeAll('dialog');
                            });
                            $("#search").click();
                        }
                    })
            }else if(event=='confirmReceipt'){
                var invoiceNo="";
                if(data.invoiceNo!=undefined){
                    invoiceNo=data.invoiceNo;
                };
                var shipmentId=data.shipmentId;
                    $.ajax({
                        url: '/web_posm/feeBusinessShipment/confirmReceipt?invoiceNo='+invoiceNo+'&shipmentId='+shipmentId,
                        //type: "POST",
                        //contentType: "application/json;charset=utf-8",
                        //data: {"invoiceNo":data.invoiceNo,"shipmentId":data.shipmentId},
                       // dataType: "json",
                        success: function (res) {
                            layer.closeAll();
                            layer.alert(res.msg, {icon: 6}, function () {
                                layer.closeAll('dialog');
                            });
                            $("#search").click();
                        }
                    })
            }else if(event=='edit'){

                var  bigType  = data.bigType;
                var newbusinessDesc = data.newbusinessDesc;
                var productType = data.productType;
                var  businessMonth =data.businessMonth;
                var  entryMonth =data.entryMonth;
                var bussinessOne =data.bussinessOne;
                var remarks= data.remarks;
                var updateOrderNumber= data.orderNumber;
                var updateMarketActivityName= data.marketActivityName;


                 $("#updateBigType").val(bigType);
                 var typeList=[]
                $.ajax({
                    url: '/web_posm/financialBusinessType/typeList',
                    type: "POST",
                    contentType: "application/json;charset=utf-8",
                    data: bigType,
                    dataType: "json",
                    success: function (res) {
                        var text=" <option value=\"\">请选择</option>-->"
                        for(var i = 0, len = res.data.length; i < len; i++) {
                            if(typeList.indexOf(res.data[i].value )!==-1){
                                continue;
                            }
                            text += "<option value='" + res.data[i].value + "'>" + res.data[i].value + "</option>";
                            typeList.push(res.data[i].value)
                        }
                        $('#updateBusinessDesc').html(text);
                        $("#updateBusinessDesc").val(newbusinessDesc);
                        layui.form.render();
                    },
                    error() {
                    }
                });
                 $("#updateBusinessEntity").val(data.busEntity);
                 $("#updateProductType").val(productType);
                 $("#updateBusinessMonth").val(businessMonth);
                 $("#updateAttributiveMonth").val(entryMonth);
                 $("#updateAccountMonth").val(bussinessOne);
                 $("#updateRemark").val(remarks);
                 $("#updateOrderNumber").val(updateOrderNumber);
                 $("#updateMarketActivityName").val(updateMarketActivityName);

                if (newbusinessDesc === '分账') {
                    console.log("province=" + data.performanceProvinceId);
                    console.log("city=" + data.performanceCityId);

                    $("#separateAccountDiv").show();
                    $("#updateSeparateAccountMerchant").val(data.separateAccountMerchant);
                    $("#updateOpeningTimeStart").val(data.openingTimeStart);
                    $("#updateOpeningTimeEnd").val(data.openingTimeEnd);

                    var province = data.performanceProvinceId;

                    if (isEmpty(province)) {
                        geographySelectQuery('updatePerformanceProvince', '/web_posm/geography/queryProvince', null, null);
                    } else {
                        geographySelectQuery('updatePerformanceProvince', '/web_posm/geography/queryProvince', null, province);

                        var city = data.performanceCityId;

                        if (isEmpty(city)) {
                            geographySelectQuery('updatePerformanceCity', '/web_posm/geography/queryCityByProvId', {"provinceId" : province}, null);
                        } else {
                            geographySelectQuery('updatePerformanceCity', '/web_posm/geography/queryCityByProvId', {"provinceId" : province}, city);
                        }
                    }

                    // if (!isEmpty(data.customerType)) {
                    //     $("#updateCustomerType").val(data.customerType);
                    // }
                } else {
                    $("#separateAccountDiv").hide();
                }
                if (!isEmpty(data.customerType)) {
                    $("#updateCustomerType").val(data.customerType);
                }
                 var shipmentId = data.shipmentId;


                layer.open({
                    title: '修改配置',
                    content: $('#updateFeeBussinessShip'),
                    type: 1,
                    area: ['490px', '500px'],
                    cancel: function (indexopen, layero) {
                        layer.closeAll();
                        return false;
                    },
                    end: function (index) {
                        layer.close(index);
                        $('#updateFeeBussinessShipForm')[0].reset();
                        form.render();
                    }
                });

                form.on('submit(updateFeeBussinessShipSubmit)',function (data) {

                    var  bigType  =  $("#updateBigType").val();
                    var newbusinessDesc =  $("#updateBusinessDesc").val();
                    var productType =    $("#updateProductType").val();
                    var  businessMonth = $("#updateBusinessMonth").val();
                    var  entryMonth = $("#updateAttributiveMonth").val();
                    var bussinessOne =    $("#updateAccountMonth").val();
                    var remarks=    $("#updateRemark").val();
                    var updateOrderNumber =    $("#updateOrderNumber").val();

                    var forData = {
                        "bigType":bigType,
                        "newbusinessDesc":newbusinessDesc,
                        "productType":productType,
                        "businessMonth":businessMonth,
                        "entryMonth":entryMonth,
                        "bussinessOne":bussinessOne,
                        "remarks":remarks,
                        "shipmentId":shipmentId,
                        "orderNumber":updateOrderNumber,
                        "marketActivityName":updateMarketActivityName,
                        "busEntity": $("#updateBusinessEntity").val(),
                        "customerType": $("#updateCustomerType").val(),
                    }

                    if (isEmpty($("#updateCustomerType").val())) {
                        layer.msg("请选择客户类别", {"icon": "2"});
                        return false;
                    }

                    if (newbusinessDesc === '分账') {
                        forData.separateAccountMerchant = $("#updateSeparateAccountMerchant").val();
                        forData.openingTimeStart = $("#updateOpeningTimeStart").val();
                        forData.openingTimeEnd = $("#updateOpeningTimeEnd").val();
                        forData.performanceProvince = $("#updatePerformanceProvince").val();
                        forData.performanceCity = $("#updatePerformanceCity").val();
                        forData.customerType = $("#updateCustomerType").val();
                    }

                    $.ajax({
                        url: '/web_posm/feeBusinessShipment/updateFeeBussinessShip',
                        data: JSON.stringify(forData),
                        type: "post",
                        dataType: "json",
                        contentType: "application/json; charset=utf-8",
                        success(res) {
                            layer.msg(res.msg);
                            if(res.code ===400){
                                return;
                            }
                            layer.closeAll();
                            $("#search").click();
                            layer.closeAll('loading'); //关闭loading
                        },
                        error() {
                            layer.closeAll('loading'); //关闭loading
                            layer.alert('获取数据失败，请检查是否部署在本地服务器环境下');
                        }
                    });
                    return false;
                })

            } else if (event === "backMoneyDetail") {
                window.shipmentId = data.shipmentId;

                layer.open({
                    type: 2,
                    title: '回款明细',
                    shadeClose: true,
                    closeBtn: 1, //不显示关闭按钮
                    area: ['1200px', '650px'],
                    content: './back-money-detail.html',
                });
            } else if(event === "applyBreakdown") {
                // 拆账申请
                initApplyBreakdown(data);
            }else if(event === "auditBreakdown") {
                // 拆账审核
                if(data.breakdownStatus === '1') { // 1-待审核
                    initAuditBreakdown(data.breakdownId);
                } else {
                    layer.msg('当前拆账记录不可审核', {icon: 2});
                }
            }else if(event === "editBreakdown") {
                // 重新拆账
                // if(data.breakdownStatus === '2') { // 2-审核拒绝
                    initReBreakdown(data.breakdownId);
                // } else {
                //     layer.msg('只有审核拒绝的记录可重新拆账', {icon: 2});
                // }
            }
        });

        // 动态添加拆账明细行
        window.addBreakDownRow = function(){
            var table = layui.table;
            var data = table.cache['accountTable'] || []; // 获取当前表格数据

            // 计算可拆账金额
            // var formData = $('#applyBreakdownShipForm').serializeObject();
            // var maxAmount = parseFloat(formData.breakDownMoney);
            // var currentTotal = calculateTotalAmount(data);

            // 检查是否还可以添加
            // if(currentTotal >= maxAmount) {
            //     layer.msg('总拆账金额已达到可拆账金额，无法继续添加');
            //     return;
            // }

            // 添加新行
            var newRow = {
                seq: data.length + 1,
                ncName: '',
                ncCode: '',
                amount: '',
                status: '0' // 初始状态为待审核
            };

            table.reload('accountTable', {
                data: data.concat([newRow])
            });
        };

        //搜索
        form.on('submit(search)', function (data) {
            if(null !== data.field.provinceIdList && "" !== data.field.provinceIdList && undefined != data.field.provinceIdList){
                data.field.provinceIdList=data.field.provinceIdList.split(",");
            }else{
                data.field.provinceIdList=null;
            }
            if(null !==data.field.cityIdList && ""!==data.field.cityIdList && undefined != data.field.cityIdList){
                data.field.cityIdList=data.field.cityIdList.split(",");
            }else{
                data.field.cityIdList=null;
            }
            queryTotalAmount(data.field, "2");

            tableBase.reload({
                url: "/web_posm/feeBusinessShipment/queryFeeBusinessShipmentDetail",  //数据接口
                page: {
                    curr: $(".layui-laypage-em").next().html() //重新从第 1 页开始
                }
                ,
                where: {
                    ...data.field,
                }
            });

            stampInt();
            return false;
        });

        //快递SN导入
        window.reloadTableFeeBusinessShipment = function () {
            //执行重载
            table.reload('feeBusinessShipmentTableId', {
                page: {
                    curr: 1 //重新从第 1 页开始
                },
            }, 'data');
        }


        // queryTotalAmount({}, "2");
        $(document).ready(function() {//等所有DOM加载完后执行
            stampInt();
            stampSelectInt();
            // 使用 URLSearchParams 解析查询字符串
            const urlParams = new URLSearchParams(window.location.search);
            if('1'===urlParams.get('stamp') && !isEmpty(urlParams.get('bigType'))){//如果是从电子签章管理跳转过来的
                var bigType = urlParams.get('bigType');
                tableBase.config.cols[0][1].hide = true; //隐藏第一列操作列

                //隐藏:
                tableBase.config.cols[0][2].hide = true;//业务流水号
                tableBase.config.cols[0][3].hide = true;//申请编号
                tableBase.config.cols[0][4].hide = true;//NC编号
                tableBase.config.cols[0][6].width = '250';//NC名称
                tableBase.config.cols[0][8].width = '130';//业务类型
                tableBase.config.cols[0][9].width = '80';//产品类型
                tableBase.config.cols[0][10].width = '105';//对应业务大类
                tableBase.config.cols[0][11].hide = true;//发票号
                tableBase.config.cols[0][12].hide = true;//确认状态
                tableBase.config.cols[0][13].hide = true;//归属月份
                tableBase.config.cols[0][15].hide = true;//调整金额
                tableBase.config.cols[0][17].hide = true;//需开票金额
                tableBase.config.cols[0][18].hide = true;//已开票金额
                tableBase.config.cols[0][19].hide = true;//可开票余额
                tableBase.config.cols[0][20].hide = true;//已回款金额
                tableBase.config.cols[0][21].hide = true;//抵扣款金额
                tableBase.config.cols[0][22].hide = true;//未回款金额
                tableBase.config.cols[0][23].hide = true;//回款日期
                tableBase.config.cols[0][24].hide = true;//回款状态

                if('手续费贴补'===bigType){
                    tableBase.config.cols[0][27].hide = true;//订单号
                    tableBase.config.cols[0][28].hide = true;//营销活动名称
                    tableBase.config.cols[0][5].width = '200';//业务主体
                    tableBase.config.cols[0][25].width = '70';//省份
                    tableBase.config.cols[0][26].width = '70';//地市
                } else if('营销活动'===bigType){
                    tableBase.config.cols[0][27].width = '160';//订单号
                    tableBase.config.cols[0][28].width = '143';//营销活动名称
                    tableBase.config.cols[0][5].width = '100';//业务主体
                    tableBase.config.cols[0][25].width = '70';//省份
                    tableBase.config.cols[0][26].width = '70';//地市
                }
                tableBase.config.cols[0][29].hide = true;//客户类别
                tableBase.config.cols[0][31].hide = true;//业务2
                tableBase.config.cols[0][32].hide = true;//业务3
                tableBase.config.cols[0][33].hide = true;//业务4
                tableBase.config.cols[0][34].hide = true;//业务5
                tableBase.config.cols[0][35].hide = true;//申请状态
                tableBase.config.cols[0][36].hide = true;//导入人
                tableBase.config.cols[0][37].hide = true;//审核状态
                tableBase.config.cols[0][38].hide = true;//申请人
                tableBase.config.cols[0][39].hide = true;//申请状态
                tableBase.config.cols[0][40].hide = true;//申请时间
                tableBase.config.cols[0][41].hide = true;//审核人
                tableBase.config.cols[0][42].hide = true;//审核状态
                tableBase.config.cols[0][43].hide = true;//审核时间
                tableBase.config.cols[0][44].hide = true;//备注
                tableBase.config.cols[0][45].hide = true;//票面备注
                tableBase.config.cols[0][46].hide = true;//签收状态
                tableBase.config.cols[0][47].hide = true;//签收人
                tableBase.config.cols[0][48].hide = true;//签收时间
                tableBase.config.cols[0][49].hide = true;//物流单号
                tableBase.config.cols[0][50].hide = true;//发票类型
                table.reload('feeBusinessShipmentTable'); // 重新加载表格以应用更改
            }
        });

        //精确代理商多选下拉框
        var xmSelCity = xmSelect.render({
            el: '#ssds',
            language: 'zn',
            name: 'cityIdList',
            toolbar: {show: true},
            paging: false,
            //配置搜索
            pageRemote: true,
            //数据处理
            remoteMethod: function (val, cb, show, pageIndex) {
                //val: 搜索框的内容, 不开启搜索默认为空, cb: 回调函数, show: 当前下拉框是否展开, pageIndex: 当前第几页
                //这里的axios类似于ajax
                axios({
                    method: 'post',
                    url: '/web_posm/institutionPostal/getCityList',
                    data: {
                        deptIdList: ""
                    }
                }).then(response => {
                    //这里是success的处理
                    var res = response.data;
                    //回调需要两个参数, 第一个: 数据数组, 第二个: 总页码
                    cb(res.data, res.count);
                }).catch(err => {
                    //这里是error的处理
                    cb([], 0);
                });
            },
        })
        //精确代理商多选下拉框
        var xmSel = xmSelect.render({
            el: '#sssf',
            language: 'zn',
            name: 'provinceIdList',
            toolbar: {show: true},
            paging: false,
            //配置搜索
            pageRemote: true,
            //数据处理
            remoteMethod: function (val, cb, show, pageIndex) {
                //val: 搜索框的内容, 不开启搜索默认为空, cb: 回调函数, show: 当前下拉框是否展开, pageIndex: 当前第几页
                //这里的axios类似于ajax
                axios({
                    method: 'post',
                    url: '/web_posm/institutionPostal/getAllProvincesSd',
                    data: {
                        departmentId: "",
                    }
                }).then(response => {
                    //这里是success的处理
                    var res = response.data;
                    //回调需要两个参数, 第一个: 数据数组, 第二个: 总页码
                    cb(res.data, res.count);
                }).catch(err => {
                    //这里是error的处理
                    cb([], 0);
                });
            },
            on: function (data) {
                var arr = data.arr;
                var list = [];
                for (let i = 0; i < arr.length; i++) {
                    list.push(arr[i].value)
                }
                axios({
                    method: 'post',
                    url: '/web_posm/institutionPostal/getCityListSd',
                    data: {
                        deptIdList: list
                    }
                }).then(response => {
                    //这里是success的处理
                    var res = response.data;
                    //回调需要两个参数, 第一个: 数据数组, 第二个: 总页码
                    xmSelCity.update({
                        data: res.data
                    })
                }).catch(err => {
                    //这里是error的处理
                    xmSelCity.update({
                        data: []
                    })
                });
            },
        });
    });

    function showMore() {
        if ($(".moreSearch").is(":hidden")) {
            $(".moreSearch").show();
            $("#icon").html("&#xe619;")
            $("#searchDiv").css("margin-top",-53);
        } else {
            $(".moreSearch").hide();
            $("#icon").html("&#xe61a;")
            $("#searchDiv").css("margin-top",-34);
        }
    }
</script>
<script type="text/html" id="toolbarDemo">
    <div >
        1.需开票金额=业务金额+调整金额
        <div style="margin-top: -11px;">
            2.可开票金额=业务金额-已开票金额+调整金额
            <div style="margin-top: -11px;">
                3.未回款金额=需开票金额-已回款金额-抵扣款金额
                <div style="margin-top: -11px;">
                    4.业务月份指业务实际发生月份：手续费贴补为贴补发生月份，手续费返还为手续费返还的划付月份，营销活动为营销活动发生核销（领用）的月份
                </div>

            </div>
        </div>

    </div>
    <div id="toolbarDemoBtns">
    <button class="layui-btn " id="importShipment" lay-event="importShipment">导入明细</button>
    <button class="layui-btn " id="exportShipment" lay-event="exportShipment">导出明细</button>
    <button class="layui-btn " id="exportModel" lay-event="exportModel">下载导入模板</button>
    <button class="layui-btn " id="batchInvoice" lay-event="batchInvoice">开票申请</button>
    <button class="layui-btn " id="entryMonth" lay-event="entryMonth">归属月份</button>
    <button class="layui-btn " id="delete" lay-event="delete">删除</button>
    <button class="layui-btn " id="confirmStatusBtn" lay-event="confirmStatus">确认</button>
    <button class="layui-btn " id="cancelConfirmStatusBtn" lay-event="cancelConfirmStatus">取消确认</button>
    </div>
</script>
<script type="text/html" id="operationTab">
    {{#  if(d.signStatus == "未签收"){ }}
    <a class="layui-btn layui-func-btn" id="confirmReceipt" lay-event="confirmReceipt">确认签收</a>
    {{#  } }}
    {{#  if(d.signStatus == "已签收"){console.log(d.signStatus); }}
    <a class="layui-btn layui-func-btn" id="cConfirmReceipt" lay-event="cConfirmReceipt">签收确认取消</a>
    {{#  } }}
    <a class="layui-btn layui-func-btn" id="edit" lay-event="edit">编辑</a>
    {{#  if(d.backedMoney.substring(0, 1) !== "." && d.backedMoney !== '0'){ }}
        <a class="layui-btn layui-func-btn" lay-event="backMoneyDetail">回款明细</a>
    {{#  } }}
    {{#  if(d.breakDownStatus == "0"){ }}
    <a class="layui-btn layui-func-btn" id="applyBreakdown" lay-event="applyBreakdown">拆账申请</a>
    {{#  } }}
    {{#  if(d.breakDownStatus == "1"){ }}
    <a class="layui-btn layui-func-btn" id="auditBreakdown" lay-event="auditBreakdown">拆账审核</a>
    {{#  } }}
    {{#  if(d.breakDownStatus != "0"){ }}
    <a class="layui-btn layui-func-btn" id="editBreakdown" lay-event="editBreakdown">重新拆账</a>
    {{#  } }}
</script>

</html>
